'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Network, 
  Search, 
  TrendingUp, 
  FileText, 
  Shield, 
  Crown, 
  Lightbulb,
  Activity,
  Users,
  Bot
} from 'lucide-react';

const KaibanJSPreview: React.FC = () => {
  const [activeAgent, setActiveAgent] = useState(0);
  const [pulseIndex, setPulseIndex] = useState(0);

  const agents = [
    { name: 'Research', icon: Search, color: 'from-blue-500 to-cyan-500' },
    { name: 'Analysis', icon: TrendingUp, color: 'from-purple-500 to-pink-500' },
    { name: 'Writing', icon: FileText, color: 'from-green-500 to-emerald-500' },
    { name: 'Quality', icon: Shield, color: 'from-orange-500 to-red-500' },
    { name: 'Supervisor', icon: Crown, color: 'from-yellow-500 to-orange-500' }
  ];

  // Cycle through agents
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveAgent((prev) => (prev + 1) % agents.length);
      setPulseIndex((prev) => (prev + 1) % agents.length);
    }, 1500);

    return () => clearInterval(interval);
  }, [agents.length]);

  return (
    <div className="relative w-full h-full min-h-[300px] bg-gradient-to-br from-black via-gray-900 to-black rounded-xl overflow-hidden">
      {/* Background grid */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(34, 197, 94, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(34, 197, 94, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }} />
      </div>

      {/* Main content */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full p-6">
        
        {/* Central KaibanJS Icon */}
        <div className="relative mb-8">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            className="relative"
          >
            <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-cyan-500 rounded-xl flex items-center justify-center">
              <Lightbulb className="w-8 h-8 text-white" />
            </div>
          </motion.div>
          
          {/* Pulse effect */}
          <motion.div
            animate={{ scale: [1, 1.5, 1], opacity: [0.8, 0, 0.8] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-cyan-500 rounded-xl"
          />
        </div>

        {/* Agent Network */}
        <div className="relative w-64 h-64 mb-6">
          {agents.map((agent, index) => {
            const Icon = agent.icon;
            const angle = (index * 360) / agents.length;
            const radius = 80;
            const x = Math.cos((angle - 90) * Math.PI / 180) * radius;
            const y = Math.sin((angle - 90) * Math.PI / 180) * radius;
            
            return (
              <motion.div
                key={agent.name}
                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                style={{
                  transform: `translate(${x}px, ${y}px) translate(-50%, -50%)`
                }}
                initial={{ scale: 0.8, opacity: 0.6 }}
                animate={{ 
                  scale: activeAgent === index ? 1.2 : 0.8,
                  opacity: activeAgent === index ? 1 : 0.6
                }}
                transition={{ duration: 0.5 }}
              >
                <div className="relative">
                  <div className={`absolute inset-0 bg-gradient-to-r ${agent.color} rounded-lg blur-sm opacity-50`} />
                  <div className="relative w-10 h-10 bg-black rounded-lg border border-white/20 flex items-center justify-center">
                    <Icon className="w-5 h-5 text-white" />
                  </div>
                  
                  {/* Agent pulse */}
                  {pulseIndex === index && (
                    <motion.div
                      initial={{ scale: 1, opacity: 1 }}
                      animate={{ scale: 2, opacity: 0 }}
                      transition={{ duration: 1 }}
                      className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-cyan-500 rounded-lg"
                    />
                  )}
                </div>
                
                {/* Agent name */}
                <div className="absolute top-12 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                  <span className="text-xs text-gray-400 font-medium">{agent.name}</span>
                </div>
              </motion.div>
            );
          })}
          
          {/* Connection lines */}
          <svg className="absolute inset-0 w-full h-full" style={{ pointerEvents: 'none' }}>
            {agents.map((_, index) => {
              const angle = (index * 360) / agents.length;
              const radius = 80;
              const x = Math.cos((angle - 90) * Math.PI / 180) * radius + 128;
              const y = Math.sin((angle - 90) * Math.PI / 180) * radius + 128;
              
              return (
                <motion.line
                  key={`line-${index}`}
                  x1="128"
                  y1="128"
                  x2={x}
                  y2={y}
                  stroke="rgba(34, 197, 94, 0.3)"
                  strokeWidth="1"
                  initial={{ pathLength: 0 }}
                  animate={{ 
                    pathLength: 1,
                    stroke: activeAgent === index ? "rgba(34, 197, 94, 0.8)" : "rgba(34, 197, 94, 0.3)"
                  }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                />
              );
            })}
          </svg>
        </div>

        {/* Status Display */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center space-x-2">
            <Activity className="w-4 h-4 text-emerald-400" />
            <span className="text-sm font-medium text-emerald-400">
              Active Agent: {agents[activeAgent].name}
            </span>
          </div>
          
          <div className="flex items-center justify-center space-x-4 text-xs text-gray-400">
            <div className="flex items-center space-x-1">
              <Users className="w-3 h-3" />
              <span>5 Agents</span>
            </div>
            <div className="flex items-center space-x-1">
              <Bot className="w-3 h-3" />
              <span>Autonomous</span>
            </div>
            <div className="flex items-center space-x-1">
              <Network className="w-3 h-3" />
              <span>Supervised</span>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="mt-6 grid grid-cols-2 gap-3 text-xs">
          <div className="bg-emerald-900/30 border border-emerald-500/30 rounded-lg p-2 text-center">
            <div className="text-emerald-400 font-medium">Multi-Agent</div>
            <div className="text-gray-400">Workflow</div>
          </div>
          <div className="bg-cyan-900/30 border border-cyan-500/30 rounded-lg p-2 text-center">
            <div className="text-cyan-400 font-medium">Autonomous</div>
            <div className="text-gray-400">Supervision</div>
          </div>
        </div>
      </div>

      {/* Animated background dots */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 h-1 bg-emerald-400 rounded-full"
          style={{
            left: `${20 + (i * 10)}%`,
            top: `${20 + (i * 8)}%`
          }}
          animate={{
            opacity: [0.3, 0.8, 0.3],
            scale: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 2 + Math.random(),
            repeat: Infinity,
            delay: i * 0.3
          }}
        />
      ))}
    </div>
  );
};

export default KaibanJSPreview; 