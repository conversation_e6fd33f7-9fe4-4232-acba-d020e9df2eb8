'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity, 
  Brain, 
  Search, 
  Globe, 
  Zap, 
  Shield, 
  Target, 
  Code, 
  Database,
  Eye,
  Cpu,
  Terminal,
  Wifi,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Clock,
  FileText
} from 'lucide-react';

interface StreamEvent {
  type: string;
  data: any;
  timestamp: number;
}

interface InvincibleStreamingUIProps {
  topic: string;
  contentLength?: number;
  tone?: string;
  targetAudience?: string;
  customInstructions?: string;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
  isSaving?: boolean;
  version?: 'v1' | 'v2' | 'autonomous';
}

const InvincibleStreamingUI: React.FC<InvincibleStreamingUIProps> = ({
  topic,
  contentLength,
  tone,
  targetAudience,
  customInstructions,
  onComplete,
  onError,
  isSaving,
  version = 'v1'
}) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentPhase, setCurrentPhase] = useState<string>('');
  const [progress, setProgress] = useState(0);
  const [events, setEvents] = useState<StreamEvent[]>([]);
  const [searchQueries, setSearchQueries] = useState<string[]>([]);
  const [scrapedDomains, setScrapedDomains] = useState<string[]>([]);
  const [analysisComplete, setAnalysisComplete] = useState<string[]>([]);
  const [humanizationStats, setHumanizationStats] = useState<Record<string, any>>({});
  const [finalResult, setFinalResult] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error' | 'complete'>('connecting');
  const eventsContainerRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // Auto-start streaming when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      startStreaming();
    }, 1000); // Small delay for dramatic effect
    
    return () => {
      clearTimeout(timer);
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  const startStreaming = () => {
    if (isStreaming) return;
    
    setIsStreaming(true);
    setProgress(0);
    setEvents([]);
    setSearchQueries([]);
    setScrapedDomains([]);
    setAnalysisComplete([]);
    setHumanizationStats({});
    setFinalResult(null);
    setError('');
    setConnectionStatus('connecting');

    // Safe JSON parsing helper
    const safeParseJSON = (rawData: string) => {
      try {
        if (!rawData || rawData === 'undefined' || rawData === 'null') {
          console.warn('Received invalid SSE data:', rawData);
          return null;
        }
        return JSON.parse(rawData);
      } catch (error) {
        console.error('Failed to parse SSE data:', rawData, error);
        return null;
      }
    };

    if (version === 'v1') {
      // V1 uses GET with query parameters for EventSource
      const params = new URLSearchParams({
        topic: topic,
        contentLength: (contentLength || 2000).toString(),
        tone: tone || 'professional',
        targetAudience: targetAudience || 'general audience',
        customInstructions: customInstructions || ''
      });

      const eventSource = new EventSource(`/api/invincible/stream?${params.toString()}`);
      eventSourceRef.current = eventSource;

      // Connection established
      eventSource.onopen = () => {
        setConnectionStatus('connected');
        addEvent('connection', { message: 'Connection established', timestamp: Date.now() });
      };

      // Handle different event types
      eventSource.addEventListener('start', (e) => {
        const data = safeParseJSON((e as MessageEvent).data);
        if (data) {
          setCurrentPhase('initialization');
          addEvent('start', data);
        }
      });

      eventSource.addEventListener('phase', (e) => {
        const data = safeParseJSON((e as MessageEvent).data);
        if (data) {
          setCurrentPhase(data.phase);
          setProgress((data.step / data.total) * 100);
          addEvent('phase', data);
        }
      });

      eventSource.addEventListener('search_query', (e) => {
        const data = safeParseJSON((e as MessageEvent).data);
        if (data && data.query) {
          setSearchQueries(prev => [...prev, data.query]);
          addEvent('search_query', data);
        }
      });

      eventSource.addEventListener('search_results', (e) => {
        const data = safeParseJSON((e as MessageEvent).data);
        if (data) {
          addEvent('search_results', data);
        }
      });

      eventSource.addEventListener('scraping', (e) => {
        const data = safeParseJSON((e as MessageEvent).data);
        if (data && data.domain) {
          setScrapedDomains(prev => [...prev, data.domain]);
          addEvent('scraping', data);
        }
      });

      eventSource.addEventListener('analysis', (e) => {
        const data = safeParseJSON((e as MessageEvent).data);
        if (data && data.type) {
          setAnalysisComplete(prev => [...prev, data.type]);
          addEvent('analysis', data);
        }
      });

      eventSource.addEventListener('humanization', (e) => {
        const data = safeParseJSON((e as MessageEvent).data);
        if (data && data.type) {
          setHumanizationStats((prev: Record<string, any>) => ({ ...prev, [data.type]: data }));
          addEvent('humanization', data);
        }
      });

      eventSource.addEventListener('progress', (e) => {
        const data = safeParseJSON((e as MessageEvent).data);
        if (data) {
          addEvent('progress', data);
        }
      });

      eventSource.addEventListener('success', (e) => {
        const data = safeParseJSON((e as MessageEvent).data);
        if (data) {
          setFinalResult(data);
          setProgress(100);
          setConnectionStatus('complete');
          addEvent('success', data);
          onComplete?.(data);
        }
      });

      eventSource.addEventListener('error', (e) => {
        const data = safeParseJSON((e as MessageEvent).data);
        if (data) {
          setError(data.error || 'An error occurred');
          addEvent('error', data);
          onError?.(data.error || 'An error occurred');
        }
      });

      eventSource.addEventListener('complete', (e) => {
        setIsStreaming(false);
        setConnectionStatus('complete');
        eventSource.close();
      });

      eventSource.onerror = (error) => {
        console.error('SSE error:', error);
        setError('Connection error - attempting to reconnect...');
        setConnectionStatus('error');
        setIsStreaming(false);
        eventSource.close();
        
        // Attempt to reconnect after 3 seconds
        setTimeout(() => {
          if (!finalResult) {
            console.log('Attempting to reconnect...');
            startStreaming();
          }
        }, 3000);
      };
    } else {
      // V2 uses POST with JSON body for streaming
      startV2Streaming();
    }
  };

  const startV2Streaming = async () => {
    try {
      const response = await fetch('/api/invincible-v2/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: topic,
          contentLength: contentLength || 2000,
          tone: tone || 'professional',
          targetAudience: targetAudience || 'general audience',
          customInstructions: customInstructions || '',
          contentType: 'article'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No reader available');
      }

      setConnectionStatus('connected');
      addEvent('connection', { message: 'V2 Connection established', timestamp: Date.now() });

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonString = line.slice(6).trim();
              
              // Skip empty lines
              if (!jsonString) continue;
              
              // Parse JSON with robust error handling
              const data = JSON.parse(jsonString);
              
              if (data && typeof data === 'object') {
                if (data.type === 'status') {
                  setCurrentPhase(data.phase);
                  addEvent('phase', data);
                } else if (data.type === 'complete') {
                  setFinalResult(data.result);
                  setProgress(100);
                  setConnectionStatus('complete');
                  addEvent('success', data.result);
                  onComplete?.(data.result);
                  setIsStreaming(false);
                  break;
                } else if (data.type === 'error') {
                  setError(data.error || 'An error occurred');
                  addEvent('error', data);
                  onError?.(data.error || 'An error occurred');
                  setIsStreaming(false);
                  break;
                }
              }
            } catch (e) {
              console.error('V2 streaming: Failed to parse JSON:', e);
              console.error('V2 streaming: Problematic line:', line);
              // Continue processing other lines
            }
          }
        }
      }
    } catch (error) {
      console.error('V2 streaming error:', error);
      console.log('V2: Falling back to non-streaming API');
      
      // Fallback to regular V2 API
      try {
        addEvent('fallback', { message: 'Falling back to non-streaming V2 API', timestamp: Date.now() });
        
        const fallbackResponse = await fetch('/api/invincible-v2', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            topic: topic,
            contentLength: contentLength || 2000,
            tone: tone || 'professional',
            targetAudience: targetAudience || 'general audience',
            customInstructions: customInstructions || '',
            contentType: 'article'
          }),
        });

        if (!fallbackResponse.ok) {
          throw new Error(`Fallback HTTP error! status: ${fallbackResponse.status}`);
        }

        const fallbackResult = await fallbackResponse.json();
        
        if (fallbackResult.success) {
          setFinalResult(fallbackResult.result);
          setProgress(100);
          setConnectionStatus('complete');
          addEvent('success', fallbackResult.result);
          onComplete?.(fallbackResult.result);
          setIsStreaming(false);
        } else {
          throw new Error(fallbackResult.error || 'V2 API failed');
        }
      } catch (fallbackError) {
        console.error('V2 fallback error:', fallbackError);
        setError('Both V2 streaming and fallback API failed');
        setConnectionStatus('error');
        setIsStreaming(false);
        onError?.('V2 execution failed');
      }
    }
  };

  const addEvent = (type: string, data: any) => {
    const event: StreamEvent = {
      type,
      data,
      timestamp: Date.now()
    };
    setEvents(prev => [...prev, event].slice(-50)); // Keep only last 50 events
  };

  // Auto-scroll events container
  useEffect(() => {
    if (eventsContainerRef.current) {
      eventsContainerRef.current.scrollTop = eventsContainerRef.current.scrollHeight;
    }
  }, [events]);

  const getPhaseInfo = (phase: string) => {
    switch (phase) {
      case 'initialization': 
        return { 
          icon: Cpu, 
          name: 'System Initialization', 
          color: 'from-blue-400 to-cyan-400',
          bgColor: 'bg-blue-500/10',
          description: 'Booting up Invincible AI systems...'
        };
      case 'primary_search': 
        return { 
          icon: Search, 
          name: 'Primary Search Protocol', 
          color: 'from-green-400 to-emerald-400',
          bgColor: 'bg-green-500/10',
          description: 'Executing competitive intelligence gathering...'
        };
      case 'content_analysis': 
        return { 
          icon: Brain, 
          name: 'Neural Content Analysis', 
          color: 'from-purple-400 to-pink-400',
          bgColor: 'bg-purple-500/10',
          description: 'Deep learning analysis in progress...'
        };
      case 'comprehensive_research': 
        return { 
          icon: Database, 
          name: 'Data Mining Operations', 
          color: 'from-orange-400 to-red-400',
          bgColor: 'bg-orange-500/10',
          description: 'Extracting knowledge from global sources...'
        };
      case 'content_generation': 
        return { 
          icon: Terminal, 
          name: 'Content Synthesis', 
          color: 'from-violet-400 to-indigo-400',
          bgColor: 'bg-violet-500/10',
          description: 'Generating superior content architecture...'
        };
      default: 
        return { 
          icon: Activity, 
          name: 'Processing', 
          color: 'from-gray-400 to-gray-500',
          bgColor: 'bg-gray-500/10',
          description: 'System operations active...'
        };
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connecting': return 'text-yellow-400';
      case 'connected': return 'text-green-400';
      case 'error': return 'text-red-400';
      case 'complete': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  const phaseInfo = getPhaseInfo(currentPhase);
  const PhaseIcon = phaseInfo.icon;

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Animated Background Matrix */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
        
        {/* Matrix-style background lines */}
        <div className="absolute inset-0 opacity-10">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-px bg-gradient-to-b from-transparent via-green-400 to-transparent"
              style={{
                left: `${(i * 5)}%`,
                height: '100%'
              }}
              animate={{
                opacity: [0.1, 0.3, 0.1],
                scaleY: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 2 + Math.random() * 3,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>

        {/* Floating particles */}
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-violet-400 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.8, 0.2],
              scale: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 3 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 3
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 p-6 max-w-7xl mx-auto">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center space-x-3 mb-4">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg opacity-70" />
              <div className="relative bg-black rounded-xl p-3 border border-violet-500/50">
                <Activity className="w-8 h-8 text-violet-400" />
              </div>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-violet-200 to-indigo-200 bg-clip-text text-transparent">
              INVINCIBLE {version === 'v1' ? 'V.1' : 'V.2'} LIVE GENERATION
            </h1>
          </div>
          
          <div className="flex items-center justify-center space-x-2 text-sm">
            <Wifi className={`w-4 h-4 ${getConnectionStatusColor()}`} />
            <span className={`${getConnectionStatusColor()} font-medium`}>
              {connectionStatus.toUpperCase()}
            </span>
            <span className="text-gray-500">•</span>
            <span className="text-gray-400">
              Real-time AI Processing
            </span>
          </div>
        </motion.div>

        {/* Topic Display */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-8"
        >
          <div className="bg-gradient-to-r from-violet-900/20 to-indigo-900/20 backdrop-blur-xl border border-violet-500/30 rounded-2xl p-6">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-xl">
                <Target className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-bold text-white mb-2">Mission Target</h2>
                <p className="text-2xl font-bold bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent mb-3">
                  "{topic}"
                </p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Length:</span>
                    <span className="text-white ml-2 font-medium">{contentLength || 2000} words</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Tone:</span>
                    <span className="text-white ml-2 font-medium">{tone || 'professional'}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Audience:</span>
                    <span className="text-white ml-2 font-medium">{targetAudience || 'general'}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Mode:</span>
                    <span className="text-green-400 ml-2 font-medium">INVINCIBLE</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Current Phase Display */}
        {isStreaming && currentPhase && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className={`${phaseInfo.bgColor} backdrop-blur-xl border border-white/10 rounded-2xl p-6`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 bg-gradient-to-r ${phaseInfo.color} rounded-xl`}>
                    <PhaseIcon className="w-6 h-6 text-black" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">{phaseInfo.name}</h3>
                    <p className="text-gray-300">{phaseInfo.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-white mb-1">{Math.round(progress)}%</div>
                  <div className="text-sm text-gray-400">Complete</div>
                </div>
              </div>
              
              <div className="relative">
                <div className="w-full bg-black/50 rounded-full h-3">
                  <motion.div
                    className={`h-3 rounded-full bg-gradient-to-r ${phaseInfo.color} relative overflow-hidden`}
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Live Stats Grid */}
        {isStreaming && (
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            {[
              { 
                icon: Search, 
                label: 'Search Queries', 
                value: searchQueries.length, 
                color: 'from-green-400 to-emerald-400',
                bgColor: 'bg-green-500/10',
                unit: 'executed'
              },
              { 
                icon: Globe, 
                label: 'Sources Analyzed', 
                value: scrapedDomains.length, 
                color: 'from-blue-400 to-cyan-400',
                bgColor: 'bg-blue-500/10',
                unit: 'websites'
              },
              { 
                icon: Brain, 
                label: 'Analysis Steps', 
                value: analysisComplete.length, 
                color: 'from-purple-400 to-pink-400',
                bgColor: 'bg-purple-500/10',
                unit: 'complete'
              },
              { 
                icon: Shield, 
                label: 'AI Bypass', 
                value: Object.keys(humanizationStats).length, 
                color: 'from-orange-400 to-red-400',
                bgColor: 'bg-orange-500/10',
                unit: 'applied'
              }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`${stat.bgColor} backdrop-blur-xl border border-white/10 rounded-xl p-4`}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <div className={`p-2 bg-gradient-to-r ${stat.color} rounded-lg`}>
                    <stat.icon className="w-5 h-5 text-black" />
                  </div>
                  <span className="text-white font-medium text-sm">{stat.label}</span>
                </div>
                <div className="text-center">
                  <motion.div 
                    className="text-3xl font-bold text-white mb-1"
                    key={stat.value}
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ type: "spring", stiffness: 500, damping: 25 }}
                  >
                    {stat.value}
                  </motion.div>
                  <div className="text-xs text-gray-400">{stat.unit}</div>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Live Activity Terminal */}
        {isStreaming && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="bg-black/60 backdrop-blur-xl border border-green-500/30 rounded-2xl overflow-hidden">
              <div className="bg-gradient-to-r from-green-900/50 to-emerald-900/50 px-6 py-4 border-b border-green-500/30">
                <div className="flex items-center space-x-3">
                  <Terminal className="w-5 h-5 text-green-400" />
                  <span className="text-green-400 font-mono font-bold">INVINCIBLE_TERMINAL</span>
                  <div className="flex space-x-2 ml-auto">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  </div>
                </div>
              </div>
              
              <div 
                ref={eventsContainerRef}
                className="h-80 overflow-y-auto p-4 space-y-2 font-mono text-sm"
                style={{ 
                  background: 'linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,20,0,0.3) 100%)'
                }}
              >
                <AnimatePresence>
                  {events.map((event, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      className="flex items-start space-x-3"
                    >
                      <span className="text-green-500 text-xs mt-1 flex-shrink-0">
                        [{new Date(event.timestamp).toLocaleTimeString()}]
                      </span>
                      <div className="flex-1">
                        <span className={`${
                          event.type === 'error' ? 'text-red-400' :
                          event.type === 'success' ? 'text-green-400' :
                          event.type === 'phase' ? 'text-blue-400' :
                          event.type === 'search_query' ? 'text-purple-400' :
                          event.type === 'humanization' ? 'text-orange-400' :
                          'text-green-300'
                        }`}>
                          {event.type === 'phase' ? '>>> ' : ''}
                          {event.data.message}
                        </span>
                        {event.data.query && (
                          <div className="text-gray-400 text-xs mt-1 pl-4">
                            QUERY: "{event.data.query}"
                          </div>
                        )}
                        {event.data.improvements && (
                          <div className="text-gray-400 text-xs mt-1 pl-4">
                            APPLIED: {event.data.improvements.join(', ')}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
                
                {/* Typing cursor */}
                {isStreaming && (
                  <motion.div
                    animate={{ opacity: [1, 0] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="text-green-400"
                  >
                    <span className="text-xs">[{new Date().toLocaleTimeString()}]</span>
                    <span className="ml-3">█</span>
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>
        )}

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="mb-8"
          >
            <div className="bg-red-900/20 backdrop-blur-xl border border-red-500/50 rounded-2xl p-6">
              <div className="flex items-center space-x-3">
                <AlertCircle className="w-6 h-6 text-red-400" />
                <div>
                  <h3 className="text-lg font-bold text-red-400 mb-1">System Alert</h3>
                  <p className="text-red-300">{error}</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Final Result */}
        {finalResult && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mb-8"
          >
            <div className="bg-gradient-to-r from-green-900/30 to-emerald-900/30 backdrop-blur-xl border border-green-500/50 rounded-2xl p-8">
              <div className="flex items-center space-x-3 mb-6">
                {isSaving ? (
                  <>
                    <div className="animate-spin w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full"></div>
                    <h3 className="text-3xl font-bold text-blue-400">SAVING ARTICLE...</h3>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-8 h-8 text-green-400" />
                    <h3 className="text-3xl font-bold text-green-400">MISSION ACCOMPLISHED</h3>
                  </>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                {[
                  { 
                    icon: FileText, 
                    label: 'Words Generated', 
                    value: finalResult.article?.wordCount || 0,
                    color: 'text-blue-400'
                  },
                  { 
                    icon: TrendingUp, 
                    label: 'SEO Score', 
                    value: `${finalResult.article?.seoScore || 0}/100`,
                    color: 'text-green-400'
                  },
                  { 
                    icon: Clock, 
                    label: 'Generation Time', 
                    value: `${Math.round((finalResult.stats?.executionTime || 0) / 1000)}s`,
                    color: 'text-purple-400'
                  }
                ].map((metric, index) => (
                  <div key={index} className="text-center">
                    <div className="flex justify-center mb-2">
                      <metric.icon className={`w-8 h-8 ${metric.color}`} />
                    </div>
                    <div className={`text-3xl font-bold ${metric.color} mb-1`}>
                      {metric.value}
                    </div>
                    <div className="text-gray-400 text-sm">{metric.label}</div>
                  </div>
                ))}
              </div>

              <div className="bg-black/40 rounded-xl p-6 border border-green-500/30">
                <h4 className="font-bold text-green-400 mb-3 flex items-center">
                  <ArrowRight className="w-5 h-5 mr-2" />
                  Generated Article Preview
                </h4>
                
                <div className="space-y-4">
                  <div>
                    <span className="text-gray-400 text-sm">TITLE:</span>
                    <p className="text-xl font-bold text-white mt-1">
                      {finalResult.article?.title}
                    </p>
                  </div>
                  
                  <div>
                    <span className="text-gray-400 text-sm">META DESCRIPTION:</span>
                    <p className="text-gray-300 mt-1 leading-relaxed">
                      {finalResult.article?.metaDescription}
                    </p>
                  </div>
                </div>
                
                {isSaving && (
                  <div className="mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-xl">
                    <div className="flex items-center space-x-3">
                      <div className="animate-spin w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full"></div>
                      <span className="text-blue-400 font-medium">
                        Saving article and redirecting to article view...
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}

      </div>
    </div>
  );
};

export default InvincibleStreamingUI; 