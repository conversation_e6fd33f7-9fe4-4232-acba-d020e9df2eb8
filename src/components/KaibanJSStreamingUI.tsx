'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity, 
  Terminal, 
  Users, 
  Brain, 
  Target, 
  CheckCircle2, 
  AlertCircle, 
  Clock, 
  Zap,
  Shield,
  Search,
  FileText,
  Settings,
  TrendingUp,
  Eye,
  Bot,
  Layers,
  Network,
  PlayCircle,
  PauseCircle,
  CircleDot,
  Sparkles,
  Crown,
  Lightbulb
} from 'lucide-react';

interface Agent {
  name: string;
  status: 'ready' | 'active' | 'completed' | 'error';
  role: string;
  progress: number;
  lastActivity?: string;
}

interface StreamEvent {
  type: 'connection' | 'init' | 'update' | 'completion' | 'content' | 'error';
  timestamp: number;
  message: string;
  data?: any;
}

interface KaibanJSStreamingUIProps {
  topic: string;
  contentLength?: number;
  tone?: string;
  targetAudience?: string;
  customInstructions?: string;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
  isSaving?: boolean;
}

const KaibanJSStreamingUI: React.FC<KaibanJSStreamingUIProps> = ({
  topic,
  contentLength,
  tone,
  targetAudience,
  customInstructions,
  onComplete,
  onError,
  isSaving
}) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentPhase, setCurrentPhase] = useState<string>('');
  const [progress, setProgress] = useState(0);
  const [events, setEvents] = useState<StreamEvent[]>([]);
  const [agents, setAgents] = useState<Agent[]>([
    { name: 'Research Specialist', status: 'ready', role: 'Primary Research & Data Collection', progress: 0 },
    { name: 'Competition Analyst', status: 'ready', role: 'Competitive Analysis & Gap Identification', progress: 0 },
    { name: 'Content Writer', status: 'ready', role: 'Superior Content Generation', progress: 0 },
    { name: 'Quality Assurance', status: 'ready', role: 'Content Quality & Optimization', progress: 0 },
    { name: 'Workflow Supervisor', status: 'ready', role: 'Autonomous Workflow Management', progress: 0 }
  ]);
  const [finalResult, setFinalResult] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error' | 'complete'>('connecting');
  const [activeAgents, setActiveAgents] = useState<number>(0);
  const [completedTasks, setCompletedTasks] = useState<number>(0);
  const [totalTasks, setTotalTasks] = useState<number>(5);
  const eventsContainerRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // Auto-start streaming when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      startStreaming();
    }, 1000);
    
    return () => {
      clearTimeout(timer);
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  // Auto-scroll events
  useEffect(() => {
    if (eventsContainerRef.current) {
      eventsContainerRef.current.scrollTop = eventsContainerRef.current.scrollHeight;
    }
  }, [events]);

  const startStreaming = async () => {
    try {
      setIsStreaming(true);
      setConnectionStatus('connecting');
      setError('');
      
      const response = await fetch('/api/kaibanjs/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic,
          contentLength,
          tone,
          targetAudience,
          customInstructions,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let buffer = ''; // Buffer to accumulate partial data

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          // Accumulate chunks in buffer
          buffer += decoder.decode(value, { stream: true });
          
          // Process complete lines from buffer
          let newlineIndex;
          while ((newlineIndex = buffer.indexOf('\n')) !== -1) {
            const line = buffer.slice(0, newlineIndex);
            buffer = buffer.slice(newlineIndex + 1);
            
            if (line.startsWith('data: ')) {
              try {
                const jsonData = line.slice(6).trim();
                if (jsonData) {
                  const data = JSON.parse(jsonData);
                  handleStreamEvent(data);
                }
              } catch (e) {
                console.error('Error parsing stream data:', e);
                console.error('Problematic line:', line);
              }
            }
          }
        }
        
        // Process any remaining data in buffer
        if (buffer.trim() && buffer.startsWith('data: ')) {
          try {
            const jsonData = buffer.slice(6).trim();
            if (jsonData) {
              const data = JSON.parse(jsonData);
              handleStreamEvent(data);
            }
          } catch (e) {
            console.error('Error parsing final stream data:', e);
            console.error('Final buffer:', buffer);
          }
        }
      }
    } catch (error) {
      console.error('Streaming error:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      setConnectionStatus('error');
      onError?.(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsStreaming(false);
    }
  };

  const handleStreamEvent = (data: any) => {
    const event: StreamEvent = {
      type: data.type,
      timestamp: data.timestamp,
      message: data.message,
      data: data
    };
    
    setEvents(prev => [...prev, event]);
    
    switch (data.type) {
      case 'connection':
        setConnectionStatus('connected');
        setCurrentPhase('Connected to KaibanJS Multi-Agent System');
        break;
        
      case 'init':
        setCurrentPhase('Initializing Agents');
        setProgress(10);
        if (data.agents) {
          setAgents(prev => prev.map(agent => ({ ...agent, status: 'ready' })));
        }
        break;
        
      case 'update':
        if (data.status) {
          setCurrentPhase(data.status.currentPhase || 'Processing');
          setCompletedTasks(data.status.completedTasks || 0);
          setTotalTasks(data.status.totalTasks || 5);
          setActiveAgents(data.status.activeAgents || 0);
          
          const completionRate = (data.status.completedTasks / data.status.totalTasks) * 100;
          setProgress(Math.min(completionRate, 90));
          
          // Update agent statuses
          if (data.status.tasks) {
            updateAgentStatuses(data.status.tasks);
          }
        }
        break;
        
      case 'completion':
        setConnectionStatus('complete');
        setProgress(100);
        setFinalResult(data);
        setCurrentPhase('Execution Complete');
        setAgents(prev => prev.map(agent => ({ ...agent, status: 'completed', progress: 100 })));
        onComplete?.(data);
        break;
        
      case 'content':
        // Handle large content sent separately
        if (data.content && finalResult) {
          const updatedResult = { ...finalResult, content: data.content };
          setFinalResult(updatedResult);
          onComplete?.(updatedResult);
        }
        break;
        
      case 'error':
        setError(data.error);
        setConnectionStatus('error');
        setCurrentPhase('Execution Failed');
        onError?.(data.error);
        break;
    }
  };

  const updateAgentStatuses = (tasks: any[]) => {
    setAgents(prev => prev.map(agent => {
      const agentTask = tasks.find(task => 
        task.agent?.name === agent.name || 
        task.description?.includes(agent.name) ||
        task.name?.includes(agent.name.split(' ')[0])
      );
      
      if (agentTask) {
        let status: 'ready' | 'active' | 'completed' | 'error' = 'ready';
        let progress = 0;
        
        switch (agentTask.status) {
          case 'pending':
            status = 'ready';
            progress = 0;
            break;
          case 'in_progress':
            status = 'active';
            progress = 50;
            break;
          case 'completed':
            status = 'completed';
            progress = 100;
            break;
          case 'failed':
            status = 'error';
            progress = 0;
            break;
        }
        
        return { ...agent, status, progress };
      }
      
      return agent;
    }));
  };

  const getAgentIcon = (agentName: string) => {
    switch (agentName) {
      case 'Research Specialist': return Search;
      case 'Competition Analyst': return TrendingUp;
      case 'Content Writer': return FileText;
      case 'Quality Assurance': return Shield;
      case 'Workflow Supervisor': return Crown;
      default: return Bot;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'text-gray-400';
      case 'active': return 'text-blue-400';
      case 'completed': return 'text-green-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready': return Clock;
      case 'active': return PlayCircle;
      case 'completed': return CheckCircle2;
      case 'error': return AlertCircle;
      default: return CircleDot;
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connecting': return 'text-yellow-400';
      case 'connected': return 'text-green-400';
      case 'error': return 'text-red-400';
      case 'complete': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Animated Background Matrix */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
        
        {/* KaibanJS-style background pattern */}
        <div className="absolute inset-0 opacity-10">
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-px bg-gradient-to-b from-transparent via-emerald-400 to-transparent"
              style={{
                left: `${(i * 7)}%`,
                height: '100%'
              }}
              animate={{
                opacity: [0.1, 0.5, 0.1],
                scaleY: [0.3, 1, 0.3]
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>

        {/* Floating particles */}
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-emerald-400 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.3, 1, 0.3],
              scale: [0.5, 1.2, 0.5]
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 p-6 max-w-7xl mx-auto">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center space-x-3 mb-4">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-600 to-cyan-600 rounded-xl blur-lg opacity-70" />
              <div className="relative bg-black rounded-xl p-3 border border-emerald-500/50">
                <Lightbulb className="w-8 h-8 text-emerald-400" />
              </div>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-emerald-200 to-cyan-200 bg-clip-text text-transparent">
              KAIBANJS V1 MULTI-AGENT SYSTEM
            </h1>
          </div>
          
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Advanced autonomous multi-agent workflow with supervisor orchestration
          </p>
          
          <div className="flex items-center justify-center space-x-6 mt-6">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${getConnectionStatusColor()}`} />
              <span className={`text-sm font-medium ${getConnectionStatusColor()}`}>
                {connectionStatus.toUpperCase()}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-emerald-400" />
              <span className="text-sm text-emerald-400">
                {activeAgents} Active Agents
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Target className="w-4 h-4 text-cyan-400" />
              <span className="text-sm text-cyan-400">
                {completedTasks}/{totalTasks} Tasks
              </span>
            </div>
          </div>
        </motion.div>

        {/* Topic Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/60 backdrop-blur-xl border border-emerald-500/30 rounded-2xl p-6 mb-8"
        >
          <div className="flex items-center space-x-3 mb-4">
            <FileText className="w-5 h-5 text-emerald-400" />
            <h2 className="text-xl font-semibold text-white">Target Topic</h2>
          </div>
          <p className="text-emerald-300 text-lg font-medium mb-4">"{topic}"</p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Length:</span>
              <span className="text-white ml-2">{contentLength || 2000} words</span>
            </div>
            <div>
              <span className="text-gray-400">Tone:</span>
              <span className="text-white ml-2">{tone || 'Professional'}</span>
            </div>
            <div>
              <span className="text-gray-400">Audience:</span>
              <span className="text-white ml-2">{targetAudience || 'General'}</span>
            </div>
            <div>
              <span className="text-gray-400">Mode:</span>
              <span className="text-emerald-400 ml-2">Autonomous</span>
            </div>
          </div>
        </motion.div>

        {/* Multi-Agent Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Agent Status Panel */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-black/60 backdrop-blur-xl border border-emerald-500/30 rounded-2xl overflow-hidden"
          >
            <div className="bg-gradient-to-r from-emerald-900/50 to-cyan-900/50 px-6 py-4 border-b border-emerald-500/30">
              <div className="flex items-center space-x-3">
                <Network className="w-5 h-5 text-emerald-400" />
                <span className="text-emerald-400 font-mono font-bold">AGENT_STATUS</span>
              </div>
            </div>
            
            <div className="p-6 space-y-4">
              {agents.map((agent, index) => {
                const AgentIcon = getAgentIcon(agent.name);
                const StatusIcon = getStatusIcon(agent.status);
                
                return (
                  <motion.div
                    key={agent.name}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-gray-900/50 rounded-xl p-4 border border-gray-700/50"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <div className={`absolute inset-0 bg-gradient-to-r from-emerald-600 to-cyan-600 rounded-lg blur-sm opacity-50`} />
                          <div className="relative bg-black rounded-lg p-2 border border-emerald-500/30">
                            <AgentIcon className="w-4 h-4 text-emerald-400" />
                          </div>
                        </div>
                        <div>
                          <h3 className="text-white font-medium">{agent.name}</h3>
                          <p className="text-gray-400 text-sm">{agent.role}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <StatusIcon className={`w-4 h-4 ${getStatusColor(agent.status)}`} />
                        <span className={`text-sm font-medium ${getStatusColor(agent.status)}`}>
                          {agent.status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                    
                    {/* Progress Bar */}
                    <div className="w-full bg-gray-700/50 rounded-full h-2">
                      <motion.div
                        className="bg-gradient-to-r from-emerald-500 to-cyan-500 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${agent.progress}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                    
                    {agent.lastActivity && (
                      <p className="text-xs text-gray-500 mt-2">{agent.lastActivity}</p>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Live Terminal */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-black/60 backdrop-blur-xl border border-emerald-500/30 rounded-2xl overflow-hidden"
          >
            <div className="bg-gradient-to-r from-emerald-900/50 to-cyan-900/50 px-6 py-4 border-b border-emerald-500/30">
              <div className="flex items-center space-x-3">
                <Terminal className="w-5 h-5 text-emerald-400" />
                <span className="text-emerald-400 font-mono font-bold">WORKFLOW_TERMINAL</span>
                <div className="flex space-x-2 ml-auto">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
              </div>
            </div>
            
            <div 
              ref={eventsContainerRef}
              className="h-96 overflow-y-auto p-4 space-y-2 font-mono text-sm"
            >
              {events.map((event, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-start space-x-3"
                >
                  <span className="text-gray-500 text-xs whitespace-nowrap">
                    {new Date(event.timestamp).toLocaleTimeString()}
                  </span>
                  <span className={`text-xs ${
                    event.type === 'error' ? 'text-red-400' :
                    event.type === 'completion' ? 'text-green-400' :
                    event.type === 'connection' ? 'text-blue-400' :
                    'text-emerald-400'
                  }`}>
                    [{event.type.toUpperCase()}]
                  </span>
                  <span className="text-gray-300 text-xs flex-1">
                    {event.message}
                  </span>
                </motion.div>
              ))}
              
              {events.length === 0 && (
                <div className="text-gray-500 text-xs">
                  Waiting for workflow events...
                </div>
              )}
            </div>
          </motion.div>
        </div>

        {/* Progress Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/60 backdrop-blur-xl border border-emerald-500/30 rounded-2xl p-6 mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Activity className="w-5 h-5 text-emerald-400" />
              <h2 className="text-xl font-semibold text-white">Workflow Progress</h2>
            </div>
            <div className="text-2xl font-bold text-emerald-400">
              {Math.round(progress)}%
            </div>
          </div>
          
          <div className="w-full bg-gray-700/50 rounded-full h-3 mb-4">
            <motion.div
              className="bg-gradient-to-r from-emerald-500 to-cyan-500 h-3 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-400">Current Phase:</span>
            <span className="text-emerald-400 font-medium">{currentPhase}</span>
          </div>
        </motion.div>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-900/30 border border-red-500/50 rounded-2xl p-6 mb-8"
          >
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-400" />
              <h2 className="text-xl font-semibold text-red-400">Execution Error</h2>
            </div>
            <p className="text-red-300 mt-2">{error}</p>
          </motion.div>
        )}

        {/* Completion Status */}
        {finalResult && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-green-900/30 border border-green-500/50 rounded-2xl p-6"
          >
            <div className="flex items-center space-x-3 mb-4">
              <CheckCircle2 className="w-5 h-5 text-green-400" />
              <h2 className="text-xl font-semibold text-green-400">Execution Complete</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Quality Score:</span>
                <span className="text-green-400 ml-2 font-bold">
                  {finalResult.insights?.qualityScore || 'N/A'}/100
                </span>
              </div>
              <div>
                <span className="text-gray-400">Execution Time:</span>
                <span className="text-green-400 ml-2">
                  {finalResult.insights?.executionTime ? 
                    `${Math.round(finalResult.insights.executionTime / 1000)}s` : 'N/A'}
                </span>
              </div>
              <div>
                <span className="text-gray-400">Tasks Completed:</span>
                <span className="text-green-400 ml-2">
                  {finalResult.insights?.totalTasks || 'N/A'}
                </span>
              </div>
            </div>
            
            {isSaving && (
              <div className="mt-4 flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-green-400 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-green-400 text-sm">Saving content...</span>
              </div>
            )}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default KaibanJSStreamingUI; 