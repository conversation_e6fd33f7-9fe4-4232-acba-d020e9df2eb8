/**
 * Competition Analysis Agent for Invincible v.2 Multi-Agent System
 * Specialized agent responsible for comprehensive competitive analysis including SEO, GEO, and AEO
 */

import { GeminiService } from '../../gemini';
import { AgentState, AgentPhase, CompetitionAgentConfig } from './types';

export class CompetitionAgent {
  private geminiService: GeminiService;
  private config: CompetitionAgentConfig;
  private agentId: string;

  constructor(config: Partial<CompetitionAgentConfig> = {}) {
    this.config = {
      analysisDepth: config.analysisDepth ?? 'comprehensive',
      includeBacklinks: config.includeBacklinks ?? true,
      includeTechnicalSeo: config.includeTechnicalSeo ?? true,
      includeContentGaps: config.includeContentGaps ?? true
    };
    
    this.agentId = 'competition-agent';
    this.geminiService = new GeminiService();
  }

  async execute(state: AgentState): Promise<AgentState> {
    const startTime = Date.now();
    this.log(state, '🏆 Competition Agent: Starting comprehensive competitive analysis');

    try {
      // Ensure we have research data from previous phase
      if (!state.primaryUrls || !state.researchData) {
        throw new Error('Research data required for competition analysis');
      }

      // Update state to competition analysis phase
      state.currentPhase = AgentPhase.COMPETITION_ANALYSIS;
      
      // Step 1: SEO Analysis - Traditional ranking factors
      const seoAnalysis = await this.performSeoAnalysis(state);
      this.log(state, '📊 SEO analysis complete');

      // Step 2: GEO Analysis - Geographic and localization factors
      const geoAnalysis = await this.performGeoAnalysis(state);
      this.log(state, '🌍 GEO analysis complete');

      // Step 3: AEO Analysis - Answer Engine Optimization
      const aeoAnalysis = await this.performAeoAnalysis(state);
      this.log(state, '🤖 AEO analysis complete');

      // Step 4: Content Gap Analysis
      const contentGaps = await this.identifyContentGaps(state);
      this.log(state, '🔍 Content gap analysis complete');

      // Step 5: Ranking Factor Analysis
      const rankingFactors = await this.analyzeRankingFactors(state);
      this.log(state, '📈 Ranking factor analysis complete');

      // Step 6: Writing Pattern Analysis
      const writingPatterns = await this.analyzeWritingPatterns(state);
      this.log(state, '✍️ Writing pattern analysis complete');

      // Compile comprehensive competition analysis
      state.competitorAnalysis = {
        seoAnalysis,
        geoAnalysis,
        aeoAnalysis,
        contentGaps,
        rankingFactors,
        writingPatterns
      };

      // Step 7: Create content plan based on all analysis
      const contentPlan = await this.createContentPlan(state);
      state.contentPlan = contentPlan;
      this.log(state, '📋 Content plan created');

      // Mark competition analysis phase as complete
      state.completedPhases.push(AgentPhase.COMPETITION_ANALYSIS);
      state.currentPhase = AgentPhase.CONTENT_PLANNING;

      const executionTime = Date.now() - startTime;
      this.log(state, `🎯 Competition Agent completed in ${executionTime}ms`);

      return state;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown competition analysis error';
      state.errors.push({
        phase: 'competition_analysis',
        error: errorMessage,
        timestamp: Date.now()
      });
      
      this.log(state, `❌ Competition Agent failed: ${errorMessage}`);
      state.currentPhase = AgentPhase.FAILED;
      
      return state;
    }
  }

  private async performSeoAnalysis(state: AgentState): Promise<any> {
    this.log(state, '🔍 Analyzing SEO parameters across competitors');

    const competitorContent = state.primaryUrls!.slice(0, 10); // Top 10 competitors
    
    const seoPrompt = `You are an expert SEO analyst examining competitor content for "${state.topic}".

**Competitor Content Analysis:**
${JSON.stringify(competitorContent.map(c => ({
  url: c.url,
  title: c.title,
  contentLength: c.content.length,
  contentPreview: c.content.substring(0, 1000)
})), null, 2)}

Perform comprehensive SEO analysis focusing on:

1. **Keyword Strategy:**
   - Primary and secondary keywords used
   - Keyword density and placement
   - Long-tail keyword opportunities
   - Semantic keyword variations

2. **Content Structure:**
   - Title tag optimization patterns
   - Header hierarchy usage (H1, H2, H3)
   - Content length distribution
   - Internal linking strategies

3. **Technical SEO Factors:**
   - URL structure patterns
   - Meta description approaches
   - Schema markup usage indicators
   - Content formatting techniques

4. **Ranking Opportunities:**
   - Under-optimized content areas
   - Missing keyword targets
   - Weak content depth areas
   - Technical improvement opportunities

5. **Content Quality Indicators:**
   - Depth of coverage
   - Authority signals
   - Freshness indicators
   - User engagement signals

Return analysis as structured JSON:
{
  "keywordStrategy": {
    "primaryKeywords": [],
    "secondaryKeywords": [],
    "opportunities": []
  },
  "contentStructure": {
    "titlePatterns": [],
    "headerUsage": {},
    "averageLength": 0,
    "linkingStrategies": []
  },
  "technicalFactors": {
    "urlPatterns": [],
    "metaApproaches": [],
    "schemaUsage": [],
    "formatTechniques": []
  },
  "opportunities": {
    "underOptimized": [],
    "missingKeywords": [],
    "weakAreas": [],
    "technicalImprovements": []
  },
  "qualityIndicators": {
    "depthScore": 0,
    "authoritySignals": [],
    "freshnessFactors": [],
    "engagementIndicators": []
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        seoPrompt,
        { temperature: 0.3, maxOutputTokens: 4096 },
        'SEO Analysis'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ SEO analysis failed: ${error}`);
      return { error: 'SEO analysis failed', rawData: competitorContent.length };
    }
  }

  private async performGeoAnalysis(state: AgentState): Promise<any> {
    this.log(state, '🌍 Analyzing GEO (Geographic Engine Optimization) factors');

    const geoPrompt = `Analyze geographic and localization optimization factors for "${state.topic}".

**Target Audience:** ${state.targetAudience || 'Global audience'}
**Content Context:** ${state.customInstructions || 'General content'}

Perform GEO analysis focusing on:

1. **Geographic Targeting:**
   - Location-specific optimization opportunities
   - Regional keyword variations
   - Local search optimization potential
   - Geographic content customization needs

2. **Localization Signals:**
   - Language and cultural adaptation requirements
   - Regional terminology preferences
   - Local business integration opportunities
   - Time zone and seasonal considerations

3. **Geographic Content Strategy:**
   - Location-based content gaps
   - Regional case study opportunities
   - Local authority building tactics
   - Geographic link building potential

Return ONLY valid JSON in this exact format:
{
  "geographicTargeting": {
    "primaryRegions": [],
    "keywordVariations": {},
    "localOptimization": [],
    "customizationNeeds": []
  },
  "localizationSignals": {
    "languageAdaptation": [],
    "culturalFactors": [],
    "businessIntegration": [],
    "seasonalConsiderations": []
  },
  "contentStrategy": {
    "locationGaps": [],
    "caseStudyOpportunities": [],
    "authorityTactics": [],
    "linkBuildingPotential": []
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        geoPrompt,
        { temperature: 0.4, maxOutputTokens: 3072 },
        'GEO Analysis'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ GEO analysis failed: ${error}`);
      return { 
        geographicTargeting: { primaryRegions: [], keywordVariations: {}, localOptimization: [], customizationNeeds: [] },
        localizationSignals: { languageAdaptation: [], culturalFactors: [], businessIntegration: [], seasonalConsiderations: [] },
        contentStrategy: { locationGaps: [], caseStudyOpportunities: [], authorityTactics: [], linkBuildingPotential: [] }
      };
    }
  }

  private async performAeoAnalysis(state: AgentState): Promise<any> {
    this.log(state, '🤖 Analyzing AEO (Answer Engine Optimization) factors');

    const aeoPrompt = `Analyze Answer Engine Optimization factors for "${state.topic}" to optimize for AI search engines like ChatGPT, Perplexity, Claude, and Gemini.

**Research Context:**
- ${state.researchData?.length || 0} research queries analyzed
- ${state.primaryUrls?.length || 0} competitor sources examined
- Target: ${state.contentLength || 2000} word content

Perform AEO analysis focusing on:

1. **AI Search Optimization:**
   - Question-answer pair opportunities
   - Featured snippet optimization potential
   - Voice search optimization factors
   - Conversational query targeting

2. **Structured Data Requirements:**
   - FAQ schema opportunities
   - How-to schema potential
   - Article schema optimization
   - Q&A page structure needs

3. **Answer Format Optimization:**
   - Direct answer structuring
   - Step-by-step format opportunities
   - List-based answer potential
   - Comparison table possibilities

4. **AI Detection Bypass Strategies:**
   - Human writing pattern requirements
   - Natural language flow needs
   - Personality injection opportunities
   - Authenticity markers needed

Return ONLY valid JSON in this exact format:
{
  "aiSearchOptimization": {
    "questionAnswerPairs": [],
    "featuredSnippetOps": [],
    "voiceSearchFactors": [],
    "conversationalQueries": []
  },
  "structuredDataNeeds": {
    "faqSchema": [],
    "howToSchema": [],
    "articleSchema": {},
    "qaPageStructure": []
  },
  "answerFormatting": {
    "directAnswers": [],
    "stepByStepOps": [],
    "listBasedAnswers": [],
    "comparisonTables": []
  },
  "bypassStrategies": {
    "humanPatterns": [],
    "naturalFlow": [],
    "personalityInjection": [],
    "authenticityMarkers": []
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        aeoPrompt,
        { temperature: 0.5, maxOutputTokens: 4096 },
        'AEO Analysis'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ AEO analysis failed: ${error}`);
      return { 
        aiSearchOptimization: { questionAnswerPairs: [], featuredSnippetOps: [], voiceSearchFactors: [], conversationalQueries: [] },
        structuredDataNeeds: { faqSchema: [], howToSchema: [], articleSchema: {}, qaPageStructure: [] },
        answerFormatting: { directAnswers: [], stepByStepOps: [], listBasedAnswers: [], comparisonTables: [] },
        bypassStrategies: { humanPatterns: [], naturalFlow: [], personalityInjection: [], authenticityMarkers: [] }
      };
    }
  }

  private async identifyContentGaps(state: AgentState): Promise<string[]> {
    this.log(state, '🔍 Identifying content gaps and opportunities');

    const competitorSample = state.primaryUrls!.slice(0, 5).map(url => ({
      title: url.title,
      contentPreview: url.content.substring(0, 1500)
    }));

    const gapPrompt = `Identify content gaps and opportunities for "${state.topic}".

**Competitor Content Sample:**
${JSON.stringify(competitorSample, null, 2)}

**Target Specifications:**
- Word Count: ${state.contentLength || 2000} words
- Audience: ${state.targetAudience || 'General audience'}
- Tone: ${state.tone || 'Professional'}
- Custom Requirements: ${state.customInstructions || 'None'}

Identify specific content gaps where competitors are weak or missing coverage:

1. **Information Gaps:** What important information is missing?
2. **Depth Gaps:** What topics need deeper coverage?
3. **Practical Gaps:** What actionable advice is missing?
4. **Current Gaps:** What recent developments are not covered?
5. **Unique Perspective Gaps:** What viewpoints are underrepresented?

Return ONLY a JSON array of specific content gaps:
["gap1", "gap2", "gap3", ...]`;

    try {
      const response = await this.geminiService.generateContent(
        gapPrompt,
        { temperature: 0.6, maxOutputTokens: 2048 },
        'Content Gap Analysis'
      );

      const gaps = this.parseJsonResponse(response.response);
      return Array.isArray(gaps) ? gaps : [];
    } catch (error) {
      this.log(state, `⚠️ Content gap analysis failed: ${error}`);
      return [
        'Comprehensive step-by-step guidance',
        'Real-world case studies and examples',
        'Current industry trends and statistics',
        'Common mistakes and how to avoid them',
        'Advanced techniques and strategies'
      ];
    }
  }

  private async analyzeRankingFactors(state: AgentState): Promise<any> {
    this.log(state, '📈 Analyzing ranking factors and optimization opportunities');

    const rankingPrompt = `Analyze ranking factors for "${state.topic}" based on top competitors.

**Competitor Analysis:**
- ${state.primaryUrls?.length || 0} top-ranking competitors analyzed
- ${state.researchData?.length || 0} research data points
- Target content length: ${state.contentLength || 2000} words

Identify key ranking factors and optimization opportunities:

1. **Content Quality Factors:**
   - Depth and comprehensiveness requirements
   - Authority and expertise signals needed
   - Freshness and update frequency
   - User engagement optimization

2. **Technical Optimization:**
   - Site speed and performance factors
   - Mobile optimization requirements
   - Core Web Vitals considerations
   - Schema and structured data needs

3. **Authority Building:**
   - Backlink profile requirements
   - Brand mention optimization
   - Expert citation opportunities
   - Social signal amplification

4. **User Experience Factors:**
   - Content readability optimization
   - Navigation and structure needs
   - Multimedia integration requirements
   - Interaction and engagement features

Return ONLY valid JSON in this exact format:
{
  "contentQuality": {
    "depthRequirements": ["requirement1", "requirement2"],
    "authoritySignals": ["signal1", "signal2"],
    "freshnessFactors": ["factor1", "factor2"],
    "engagementOptimization": ["optimization1", "optimization2"]
  },
  "technicalOptimization": {
    "performanceFactors": ["factor1", "factor2"],
    "mobileRequirements": ["requirement1", "requirement2"],
    "coreWebVitals": ["vital1", "vital2"],
    "structuredData": ["data1", "data2"]
  },
  "authorityBuilding": {
    "backlinkProfile": ["profile1", "profile2"],
    "brandMentions": ["mention1", "mention2"],
    "expertCitations": ["citation1", "citation2"],
    "socialSignals": ["signal1", "signal2"]
  },
  "userExperience": {
    "readabilityOptimization": ["optimization1", "optimization2"],
    "structureNeeds": ["need1", "need2"],
    "multimediaRequirements": ["requirement1", "requirement2"],
    "interactionFeatures": ["feature1", "feature2"]
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        rankingPrompt,
        { temperature: 0.4, maxOutputTokens: 3072 },
        'Ranking Factor Analysis'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ Ranking factor analysis failed: ${error}`);
      return { 
        contentQuality: { depthRequirements: [], authoritySignals: [], freshnessFactors: [], engagementOptimization: [] },
        technicalOptimization: { performanceFactors: [], mobileRequirements: [], coreWebVitals: [], structuredData: [] },
        authorityBuilding: { backlinkProfile: [], brandMentions: [], expertCitations: [], socialSignals: [] },
        userExperience: { readabilityOptimization: [], structureNeeds: [], multimediaRequirements: [], interactionFeatures: [] }
      };
    }
  }

  private async analyzeWritingPatterns(state: AgentState): Promise<any> {
    this.log(state, '✍️ Analyzing human writing patterns for AI detection bypass');

    const writingPrompt = `Analyze human writing patterns from top-ranking content for "${state.topic}".

**Competitor Content:** ${state.primaryUrls?.length || 0} sources analyzed

Identify human writing characteristics to emulate for bypassing AI detection:

1. **Sentence Structure Patterns:**
   - Sentence length variation
   - Complex vs simple sentence ratios
   - Transition phrase usage
   - Paragraph structure diversity

2. **Vocabulary and Tone:**
   - Industry-specific terminology
   - Conversational elements
   - Personal voice indicators
   - Emotional language usage

3. **Content Flow Patterns:**
   - Introduction/conclusion styles
   - Logical progression methods
   - Tangent and digression usage
   - Question and answer integration

4. **Authenticity Markers:**
   - Personal experience references
   - Opinion and perspective sharing
   - Uncertainty expressions
   - Casual language elements

Return ONLY valid JSON in this exact format:
{
  "sentencePatterns": {
    "averageLength": 15,
    "variationRange": "8-25",
    "complexToSimpleRatio": "40:60",
    "commonTransitions": ["however", "meanwhile", "additionally"]
  },
  "vocabularyTone": {
    "industryTerms": ["term1", "term2"],
    "conversationalElements": ["element1", "element2"],
    "personalVoiceIndicators": ["indicator1", "indicator2"]
  },
  "contentFlow": {
    "introStyle": "description",
    "conclusionStyle": "description",
    "progressionMethod": "description"
  },
  "authenticityMarkers": {
    "personalReferences": ["marker1", "marker2"],
    "uncertaintyExpressions": ["maybe", "perhaps"],
    "casualLanguage": ["honestly", "basically"]
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        writingPrompt,
        { temperature: 0.5, maxOutputTokens: 3072 },
        'Writing Pattern Analysis'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ Writing pattern analysis failed: ${error}`);
      return { 
        sentencePatterns: { averageLength: 15, variationRange: "8-25", complexToSimpleRatio: "40:60", commonTransitions: [] },
        vocabularyTone: { industryTerms: [], conversationalElements: [], personalVoiceIndicators: [] },
        contentFlow: { introStyle: "direct", conclusionStyle: "summary", progressionMethod: "logical" },
        authenticityMarkers: { personalReferences: [], uncertaintyExpressions: [], casualLanguage: [] }
      };
    }
  }

  private async createContentPlan(state: AgentState): Promise<any> {
    this.log(state, '📋 Creating content plan based on competition analysis');

    const planPrompt = `Create a comprehensive content plan for "${state.topic}" based on extensive competition analysis.

**Analysis Results:**
- Content Gaps: ${JSON.stringify(state.competitorAnalysis?.contentGaps, null, 2)}
- SEO Analysis: ${state.competitorAnalysis?.seoAnalysis ? 'Complete' : 'Fallback'}
- Ranking Factors: ${state.competitorAnalysis?.rankingFactors ? 'Available' : 'Basic'}
- Writing Patterns: ${state.competitorAnalysis?.writingPatterns ? 'Available' : 'Basic'}

**Target Specifications:**
- Word Count: ${state.contentLength || 2000} words
- Audience: ${state.targetAudience || 'General audience'}
- Tone: ${state.tone || 'Professional'}
- Content Type: ${state.contentType || 'article'}

Return ONLY valid JSON in this exact format:
{
  "articleType": "comprehensive guide",
  "structure": [
    "introduction",
    "main_sections",
    "conclusion"
  ],
  "keyPoints": [
    "Point 1",
    "Point 2",
    "Point 3"
  ],
  "targetWordCount": ${state.contentLength || 2000},
  "seoStrategy": {
    "primaryKeywords": [],
    "secondaryKeywords": [],
    "headerStrategy": [],
    "metaApproach": ""
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        planPrompt,
        { temperature: 0.4, maxOutputTokens: 2048 },
        'Content Plan Creation'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ Content plan creation failed: ${error}`);
      return {
        articleType: 'comprehensive guide',
        structure: ['introduction', 'main_content', 'practical_examples', 'conclusion'],
        keyPoints: state.competitorAnalysis?.contentGaps || [
          'Comprehensive overview of the topic',
          'Step-by-step guidance',
          'Real-world examples',
          'Best practices and tips'
        ],
        targetWordCount: state.contentLength || 2000,
        seoStrategy: {
          primaryKeywords: state.keywords || [],
          secondaryKeywords: [],
          headerStrategy: ['H1 for title', 'H2 for main sections', 'H3 for subsections'],
          metaApproach: 'Focus on user intent and search queries'
        }
      };
    }
  }

  private parseJsonResponse(response: string): any {
    try {
      // Remove markdown code blocks if present
      let cleanResponse = response.trim();
      
      // Remove ```json and ``` markers
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // If response starts with conversational text, try to extract JSON
      if (cleanResponse.startsWith('Here\'s') || cleanResponse.startsWith('Here is')) {
        // Look for JSON object in the response
        const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          cleanResponse = jsonMatch[0];
        }
      }
      
      // Try to parse the cleaned response
      return JSON.parse(cleanResponse.trim());
    } catch (error) {
      // If JSON parsing fails, return a fallback structure
      console.warn('Failed to parse JSON response, using fallback:', error);
      return {
        error: 'Failed to parse response',
        rawResponse: response.substring(0, 500) + '...'
      };
    }
  }

  private log(state: AgentState, message: string): void {
    const logEntry = {
      agent: this.agentId,
      message,
      timestamp: Date.now()
    };
    
    state.logs.push(logEntry);
    console.log(`[${this.agentId}] ${message}`);
  }

  getCapabilities() {
    return {
      name: 'Competition Analysis Agent',
      description: 'Comprehensive SEO, GEO, and AEO competitive analysis specialist',
      inputTypes: ['primaryUrls', 'researchData', 'topic', 'targetAudience'],
      outputTypes: ['competitorAnalysis', 'seoAnalysis', 'geoAnalysis', 'aeoAnalysis'],
      dependencies: ['gemini-service'],
      parallel: false
    };
  }

  getMetrics(state: AgentState) {
    const competitionLogs = state.logs.filter(log => log.agent === this.agentId);
    return {
      executionTime: competitionLogs.length > 0 ? Date.now() - competitionLogs[0].timestamp : 0,
      successRate: state.errors.filter(e => e.phase === 'competition_analysis').length === 0 ? 100 : 0,
      qualityScore: state.competitorAnalysis ? 85 : 0,
      retryCount: state.retryCount,
      errorCount: state.errors.filter(e => e.phase === 'competition_analysis').length,
      lastExecution: Date.now()
    };
  }
} 