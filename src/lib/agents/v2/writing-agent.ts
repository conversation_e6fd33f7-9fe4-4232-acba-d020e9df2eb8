/**
 * Writing Agent for Invincible v.2 Multi-Agent System
 * Specialized agent responsible for generating superior, human-like content that bypasses AI detection
 */

import { GeminiService } from '../../gemini';
import { AgentState, AgentPhase, WritingAgentConfig } from './types';

export class WritingAgent {
  private geminiService: GeminiService;
  private config: WritingAgentConfig;
  private agentId: string;

  constructor(config: Partial<WritingAgentConfig> = {}) {
    this.config = {
      humanizationLevel: config.humanizationLevel ?? 'maximum',
      creativityLevel: config.creativityLevel ?? 0.8,
      seoOptimization: config.seoOptimization ?? true,
      externalLinking: config.externalLinking ?? true,
      tableGeneration: config.tableGeneration ?? true
    };
    
    this.agentId = 'writing-agent';
    this.geminiService = new GeminiService();
  }

  async execute(state: AgentState): Promise<AgentState> {
    const startTime = Date.now();
    this.log(state, '✍️ Writing Agent: Starting superior content generation');

    try {
      // Ensure we have all required data from previous phases
      if (!state.competitorAnalysis || !state.contentPlan) {
        throw new Error('Competition analysis and content plan required for content generation');
      }

      // Update state to content generation phase
      state.currentPhase = AgentPhase.CONTENT_GENERATION;
      
      // Step 1: Create content strategy based on all analyses
      const contentStrategy = await this.createContentStrategy(state);
      this.log(state, '📋 Content strategy created');

      // Step 2: Generate superior article content
      const generatedContent = await this.generateSuperiorContent(state, contentStrategy);
      this.log(state, '📝 Superior content generated');

      // Step 3: Apply AI detection bypass techniques
      const humanizedContent = await this.applyHumanizationTechniques(state, generatedContent);
      this.log(state, '🤖 AI detection bypass applied');

      // Step 4: Optimize for SEO and AEO
      const optimizedContent = await this.applySeoAeoOptimization(state, humanizedContent);
      this.log(state, '📊 SEO/AEO optimization complete');

      // Step 5: Final content enhancement
      const finalContent = await this.enhanceContentFinal(state, optimizedContent);
      
      // Store generated content in state
      state.generatedContent = finalContent;

      // Mark content generation phase as complete
      state.completedPhases.push(AgentPhase.CONTENT_GENERATION);
      state.currentPhase = AgentPhase.QUALITY_ASSURANCE;

      const executionTime = Date.now() - startTime;
      this.log(state, `🎯 Writing Agent completed in ${executionTime}ms`);

      return state;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown content generation error';
      state.errors.push({
        phase: 'content_generation',
        error: errorMessage,
        timestamp: Date.now()
      });
      
      this.log(state, `❌ Writing Agent failed: ${errorMessage}`);
      state.currentPhase = AgentPhase.FAILED;
      
      return state;
    }
  }

  private async createContentStrategy(state: AgentState): Promise<any> {
    this.log(state, '🎯 Creating comprehensive content strategy');

    const strategyPrompt = `Create a comprehensive content strategy for "${state.topic}".

**Available Data:**
- Research Data: ${state.researchData?.length || 0} sources
- Primary URLs: ${state.primaryUrls?.length || 0} competitors
- Content Plan: ${JSON.stringify(state.contentPlan, null, 2)}
- Competition Analysis: Available
- SEO Analysis: ${state.competitorAnalysis?.seoAnalysis ? 'Available' : 'Unavailable'}
- AEO Analysis: ${state.competitorAnalysis?.aeoAnalysis ? 'Available' : 'Unavailable'}
- Writing Patterns: ${state.competitorAnalysis?.writingPatterns ? 'Available' : 'Unavailable'}

**Target Specifications:**
- Word Count: ${state.contentLength || 2000} words
- Audience: ${state.targetAudience || 'General audience'}
- Tone: ${state.tone || 'Professional'}
- Custom Instructions: ${state.customInstructions || 'None'}

Create a detailed content strategy including:

1. **Content Architecture:**
   - Article structure and flow
   - Section breakdown with word allocations
   - Key messaging hierarchy
   - Call-to-action strategy

2. **SEO Strategy:**
   - Primary and secondary keyword integration
   - Title and meta description approach
   - Header optimization strategy
   - Internal linking opportunities

3. **AEO Strategy:**
   - Question-answer formatting
   - Featured snippet optimization
   - Voice search optimization
   - Conversational query targeting

4. **Humanization Strategy:**
   - Personal voice integration
   - Authenticity markers
   - Conversational elements
   - Emotion and personality injection

5. **Authority Building:**
   - Expert positioning tactics
   - Credibility signals
   - Trust-building elements
   - Thought leadership angles

Return as detailed JSON structure for content generation.`;

    try {
      const response = await this.geminiService.generateContent(
        strategyPrompt,
        { temperature: this.config.creativityLevel, maxOutputTokens: 4096 },
        'Content Strategy Creation'
      );

      return this.parseJsonResponse(response.response, state);
    } catch (error) {
      this.log(state, `⚠️ Content strategy creation failed: ${error}`);
      return this.createFallbackStrategy(state);
    }
  }

  private parseJsonResponse(response: string, state?: AgentState): any {
    try {
      // Remove markdown code blocks if present
      let cleanResponse = response.trim();
      
      // Remove ```json and ``` markers
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // Remove any trailing commas or malformed JSON elements
      cleanResponse = cleanResponse.replace(/,\s*}/g, '}').replace(/,\s*]/g, ']');
      
      // Fix unterminated strings by ensuring quotes are properly closed
      const quoteMatches = (cleanResponse.match(/"/g) || []).length;
      if (quoteMatches % 2 !== 0) {
        // Odd number of quotes - try to close the last unterminated string
        cleanResponse = cleanResponse.trim();
        if (!cleanResponse.endsWith('"') && !cleanResponse.endsWith('"}') && !cleanResponse.endsWith('"]')) {
          cleanResponse += '"';
        }
      }
      
      // Ensure proper JSON object closure
      if (cleanResponse.trim().startsWith('{') && !cleanResponse.trim().endsWith('}')) {
        cleanResponse = cleanResponse.trim() + '}';
      }
      
      // Try to parse the cleaned response
      return JSON.parse(cleanResponse.trim());
    } catch (error) {
      // If JSON parsing fails, return a proper fallback structure
      console.warn('Failed to parse JSON response, using fallback strategy:', error);
      
      // If we have state context, use the proper fallback strategy
      if (state) {
        return this.createFallbackStrategy(state);
      }
      
      // Generic fallback for cases without state
      return {
        contentArchitecture: {
          structure: ['Introduction', 'Main Content', 'Conclusion'],
          wordAllocation: [300, 1400, 300],
          messaging: 'Comprehensive coverage of the topic',
          callToAction: 'Expert guidance and actionable insights'
        },
        seoStrategy: {
          primaryKeyword: 'guide',
          secondaryKeywords: ['tips', 'best practices'],
          titleApproach: 'How-to format with primary keyword',
          metaApproach: 'Benefit-focused with primary keyword'
        },
        humanizationStrategy: {
          personalVoice: 'Expert but approachable',
          authenticityMarkers: ['personal experience', 'honest opinions'],
          conversationalElements: ['rhetorical questions', 'casual transitions'],
          emotionInjection: 'Enthusiasm and helpfulness'
        }
      };
    }
  }

  private async generateSuperiorContent(state: AgentState, strategy: any): Promise<any> {
    this.log(state, '🚀 Generating superior content with full context');

    const targetWordCount = state.contentLength || 2000;
    const massivePrompt = `You are a world-class content strategist and writer creating the definitive article on "${state.topic}". You have access to comprehensive research, competition analysis, and human writing patterns. Create content that will rank #1 and pass all AI detection systems.

📅 **CURRENT DATE:** ${new Date().toLocaleDateString()} - Ensure all information is current and relevant.

🎯 **CRITICAL WORD COUNT REQUIREMENT:**
- Target Word Count: EXACTLY ${targetWordCount} words
- STRICT REQUIREMENT: Your content MUST be between ${Math.floor(targetWordCount * 0.95)} and ${Math.ceil(targetWordCount * 1.05)} words
- Count your words as you write and STOP when you reach the target
- Do NOT exceed the word count - quality over quantity
- This is NOT a minimum - this is the EXACT target

🎯 **ARTICLE SPECIFICATIONS:**
- Topic: "${state.topic}"
- Article Type: ${state.contentPlan?.articleType || 'comprehensive guide'}
- Word Count: EXACTLY ${targetWordCount} words (±5% maximum)
- Tone: ${state.tone || 'professional'}
- Target Audience: ${state.targetAudience || 'general audience'}

🏆 **COMPREHENSIVE COMPETITION ANALYSIS:**
${JSON.stringify(state.competitorAnalysis, null, 2)}

📚 **RESEARCH DATA SUMMARY:**
- ${state.researchData?.length || 0} research queries executed
- ${state.primaryUrls?.length || 0} competitor sources analyzed
- Comprehensive data available across multiple topics

🔬 **ADVANCED AI DETECTION BYPASS STRATEGIES:**
1. **PERPLEXITY ENHANCEMENT (Critical):**
   - Mix sentence complexity unpredictably: "Short ones. Followed by longer, more complex sentences that add context and depth to your points."
   - Use unexpected word choices and surprising transitions
   - Include natural tangents: "Speaking of which..." or "That reminds me..."
   - Start sentences with "And", "But", "So" occasionally
   - Self-correct: "Well, actually..." or "Let me rephrase that..."

2. **BURSTINESS MASTERY:**
   - Vary sentence length dramatically (3-40 words)
   - Mix paragraph sizes (1-8 sentences)
   - Fluctuate information density
   - Add emotional ups and downs

3. **CONVERSATIONAL AUTHENTICITY:**
   - Use filler words naturally: "basically", "honestly", "actually", "pretty much"
   - Include qualifying language: "I think", "maybe", "kind of", "sort of"
   - Add parenthetical thoughts (like this one)
   - Use rhetorical questions that you then answer
   - Include personal markers: "from my experience", "personally", "real talk"

📝 **CONTENT STRATEGY:**
${JSON.stringify(strategy, null, 2)}

✍️ **HUMAN WRITING IDENTITY:**
- You are a real person with genuine expertise and passion for your subjects
- You have personal experiences, opinions, and emotional investment in helping readers
- You write like you're having a conversation with a knowledgeable friend who needs practical help
- You focus on what works in real life, not theoretical concepts
- You have personality quirks, preferences, and a distinctive voice

🚫 **BANNED AI JARGON (NEVER USE THESE):**
❌ "dive into", "delve into", "unleash", "harness", "cutting-edge"
❌ "revolutionary", "game-changing", "seamless", "robust", "leverage"
❌ "elevate", "empower", "optimize", "transform", "unlock"
❌ "comprehensive guide", "ultimate resource", "master the art"
❌ "in today's fast-paced world", "in the digital age", "ever-evolving landscape"
❌ "tapestry", "journey", "resonate", "testament", "beacon", "interplay"

✅ **NATURAL LANGUAGE REPLACEMENTS:**
✅ Instead of "leverage" → use "use", "apply", "take advantage of"
✅ Instead of "optimize" → use "improve", "make better", "fine-tune"
✅ Instead of "comprehensive" → use "complete", "detailed", "thorough"
✅ Instead of "innovative" → use "new", "creative", "different"

🎨 **AUTHENTIC HUMAN VOICE:**
- Express genuine opinions: "I honestly think...", "In my experience..."
- Share personal stories: "I remember when...", "Just last month, I..."
- Show real emotions: "I was surprised to find...", "What frustrated me was..."
- Use everyday language: "Here's the deal...", "The thing is..."
- Include natural hesitations: "I'm not 100% sure, but...", "From what I've seen..."

**CRITICAL INSTRUCTIONS:**
1. Write EXACTLY ${targetWordCount} words - no more, no less (±5% maximum tolerance)
2. Count your words as you write and stop when you reach the target
3. Write content that feels like advice from a knowledgeable friend who genuinely wants to help
4. Include specific examples with real numbers, not vague claims
5. Use the research data to provide unique insights not found in competitors
6. Apply all humanization techniques to bypass AI detection
7. Optimize for both traditional SEO and modern AEO (Answer Engine Optimization)
8. Create content that definitively outranks all competitors

**OUTPUT FORMAT:**
\`\`\`markdown
# [Compelling, SEO-Optimized Title]

**Meta Description:** [Natural, conversational description under 155 chars - avoid AI jargon, sound like a real person recommending content]

[Write the complete article following all requirements above - EXACTLY ${targetWordCount} words]
\`\`\`

REMEMBER: Your content must be EXACTLY ${targetWordCount} words. Do not exceed this limit.`;

    try {
      // Calculate optimal token allocation 
      const baseTokensNeeded = Math.ceil(targetWordCount * 2.5); // ~2.5 tokens per word for content
      const thinkingBudget = Math.min(2000, Math.floor(baseTokensNeeded * 0.3)); // 30% of content tokens for thinking, max 2000
      const maxOutputTokens = Math.min(8192, baseTokensNeeded + thinkingBudget); // Content + thinking tokens

      this.log(state, `🧠 Token allocation: ${thinkingBudget} thinking, ${maxOutputTokens} total, targeting ${targetWordCount} words`);

      const response = await this.geminiService.generateContentWithThinking(
        massivePrompt,
        thinkingBudget, // Balanced thinking budget
        false,
        { 
          temperature: this.config.creativityLevel, 
          maxOutputTokens: maxOutputTokens
        }
      );

      // Check if response is empty and implement fallback
      if (!response.response || response.response.trim().length === 0) {
        this.log(state, `⚠️ Empty response from Gemini, attempting fallback without thinking`);
        
        // Fallback: Try without thinking to maximize response tokens
        const fallbackResponse = await this.geminiService.generateContentWithoutThinking(
          massivePrompt,
          { 
            temperature: this.config.creativityLevel, 
            maxOutputTokens: Math.min(8192, baseTokensNeeded)
          }
        );
        
        if (!fallbackResponse.response || fallbackResponse.response.trim().length === 0) {
          throw new Error('Gemini returned empty response even with fallback');
        }
        
        return this.parseGeneratedContent(fallbackResponse.response);
      }

      return this.parseGeneratedContent(response.response);
    } catch (error) {
      this.log(state, `⚠️ Content generation failed: ${error}`);
      throw error;
    }
  }

  private async applyHumanizationTechniques(state: AgentState, content: any): Promise<any> {
    this.log(state, '🤖 Applying advanced humanization techniques');

    // Check if content is empty
    if (!content || !content.content || content.content.trim().length === 0) {
      this.log(state, '⚠️ Cannot humanize empty content, returning as-is');
      return content || { content: '' };
    }

    const humanizationPrompt = `Apply advanced humanization techniques to this content to maximize AI detection bypass.

**Original Content:**
${content.content}

**Humanization Level:** ${this.config.humanizationLevel}

Apply these specific techniques:

1. **Sentence Variety:** Ensure dramatic variation in sentence length and structure
2. **Natural Imperfections:** Add subtle grammatical quirks that humans naturally make
3. **Personality Injection:** Include personal opinions, experiences, and emotional reactions
4. **Conversational Flow:** Use natural transitions and casual language
5. **Authenticity Markers:** Add uncertainty expressions and qualifying language

**Important:** Maintain all factual accuracy and SEO optimization while making it sound genuinely human-written.

Return the enhanced content in the same format.`;

    try {
      const response = await this.geminiService.generateContent(
        humanizationPrompt,
        { temperature: 0.8, maxOutputTokens: 8192 },
        'Content Humanization'
      );

      return this.parseGeneratedContent(response.response);
    } catch (error) {
      this.log(state, `⚠️ Humanization failed, using original: ${error}`);
      return content;
    }
  }

  private async applySeoAeoOptimization(state: AgentState, content: any): Promise<any> {
    this.log(state, '📊 Applying SEO and AEO optimization');

    // Check if content is empty
    if (!content || !content.content || content.content.trim().length === 0) {
      this.log(state, '⚠️ Cannot optimize empty content, returning as-is');
      return content || { content: '' };
    }

    if (!this.config.seoOptimization) {
      return content;
    }

    const optimizationPrompt = `Optimize this content for both traditional SEO and modern AEO (Answer Engine Optimization).

**Content:**
${content.content}

**SEO Insights:**
${JSON.stringify(state.competitorAnalysis?.seoAnalysis, null, 2)}

**AEO Insights:**
${JSON.stringify(state.competitorAnalysis?.aeoAnalysis, null, 2)}

**Target Keywords:** ${state.keywords?.join(', ') || state.topic}

Apply these optimizations:

1. **SEO Optimization:**
   - Natural keyword integration
   - Optimized headers and subheaders
   - Meta description enhancement
   - Internal linking opportunities

2. **AEO Optimization:**
   - Question-answer formatting
   - Featured snippet structures
   - Voice search optimization
   - Conversational query alignment

   3. **Content Structure:**
   - FAQ section optimization
   - How-to section integration
   - Article flow improvement

**Critical:** Maintain natural, human-like flow while applying optimizations.

Return the optimized content in the same format.`;

    try {
      const response = await this.geminiService.generateContent(
        optimizationPrompt,
        { temperature: 0.6, maxOutputTokens: 8192 },
        'SEO/AEO Optimization'
      );

      return this.parseGeneratedContent(response.response);
    } catch (error) {
      this.log(state, `⚠️ SEO/AEO optimization failed: ${error}`);
      return content;
    }
  }

  private async enhanceContentFinal(state: AgentState, content: any): Promise<any> {
    this.log(state, '🎨 Applying final content enhancements');

    // Check if content is empty or malformed
    if (!content || !content.content || content.content.trim().length === 0) {
      this.log(state, '⚠️ Cannot enhance empty content, creating fallback');
      
      const fallbackContent = {
        title: `${state.topic}: Complete Guide`,
        content: `# ${state.topic}: Complete Guide\n\nThis article could not be generated due to a technical error. Please try again.`,
        metaDescription: `A comprehensive guide to ${state.topic}`,
        keywords: [state.topic],
        wordCount: 15
      };
      
      this.log(state, `⚠️ Fallback content created: ${fallbackContent.wordCount} words`);
      return fallbackContent;
    }

    // Check if content is malformed (contains error indicators)
    const contentText = content.content;
    if (typeof contentText !== 'string' || 
        contentText.includes('Failed to parse response') ||
        contentText.includes('error') && contentText.length < 100) {
      this.log(state, '⚠️ Detected malformed content, creating fallback');
      
      const fallbackContent = {
        title: `${state.topic}: Complete Guide`,
        content: `# ${state.topic}: Complete Guide\n\nThis article could not be generated due to a content processing error. Please try again.`,
        metaDescription: `A comprehensive guide to ${state.topic}`,
        keywords: [state.topic],
        wordCount: 15
      };
      
      this.log(state, `⚠️ Fallback content created for malformed input: ${fallbackContent.wordCount} words`);
      return fallbackContent;
    }

    // Extract and enhance metadata
    const finalContent = {
      title: this.extractTitle(content.content) || `${state.topic}: Complete Guide`,
      content: content.content,
      metaDescription: this.extractMetaDescription(content.content) || this.generateMetaDescription(state.topic, content.content),
      keywords: this.extractKeywords(content.content, state.topic),
      wordCount: this.countWords(content.content)
    };

    this.log(state, `✅ Final content: ${finalContent.wordCount} words, optimized and humanized`);
    
    return finalContent;
  }

  private parseGeneratedContent(response: string): any {
    try {
      // Extract content from markdown code blocks
      const markdownMatch = response.match(/```markdown\n([\s\S]*?)\n```/);
      if (markdownMatch) {
        return { content: markdownMatch[1] };
      }
      
      // If no markdown blocks, return the full response
      return { content: response };
    } catch (error) {
      return { content: response };
    }
  }

  private extractTitle(content: string): string | null {
    const titleMatch = content.match(/^#\s+(.+)$/m);
    return titleMatch ? titleMatch[1] : null;
  }

  private extractMetaDescription(content: string): string | null {
    const metaMatch = content.match(/\*\*Meta Description:\*\*\s*(.+)/);
    return metaMatch ? metaMatch[1] : null;
  }

  private generateMetaDescription(topic: string, content: string): string {
    const firstParagraph = content.split('\n').find(line => line.trim().length > 50) || '';
    return firstParagraph.substring(0, 155).trim() + '...';
  }

  private extractKeywords(content: string, topic: string): string[] {
    // Simple keyword extraction based on frequency and relevance
    const words = content.toLowerCase().match(/\b[a-zA-Z]{3,}\b/g) || [];
    const frequency: { [key: string]: number } = {};
    
    words.forEach(word => {
      if (!this.isStopWord(word)) {
        frequency[word] = (frequency[word] || 0) + 1;
      }
    });

    const sortedWords = Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);

    return [topic.toLowerCase(), ...sortedWords];
  }

  private isStopWord(word: string): boolean {
    const stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall', 'this', 'that', 'these', 'those'];
    return stopWords.includes(word.toLowerCase());
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).length;
  }

  private async adjustWordCount(state: AgentState, content: any, targetWords: number): Promise<any> {
    const currentWords = this.countWords(content.content);
    const adjustment = targetWords - currentWords;

    if (Math.abs(adjustment) < 50) {
      return content; // Close enough
    }

    const adjustmentPrompt = `Adjust this content to exactly ${targetWords} words (currently ${currentWords} words).

**Content:**
${content.content}

**Required Adjustment:** ${adjustment > 0 ? `Add ${adjustment} words` : `Remove ${Math.abs(adjustment)} words`}

**Instructions:**
- Maintain all key information and SEO optimization
- Keep the natural, human-like tone
- ${adjustment > 0 ? 'Add relevant details, examples, or explanations' : 'Remove redundant or less important content'}
- Ensure the final word count is exactly ${targetWords} words

Return the adjusted content in the same format.`;

    try {
      const response = await this.geminiService.generateContent(
        adjustmentPrompt,
        { temperature: 0.5, maxOutputTokens: 8192 },
        'Word Count Adjustment'
      );

      return this.parseGeneratedContent(response.response);
    } catch (error) {
      this.log(state, `⚠️ Word count adjustment failed: ${error}`);
      return content;
    }
  }

  private createFallbackStrategy(state: AgentState): any {
    return {
      contentArchitecture: {
        structure: ['Introduction', 'Main Content', 'Conclusion'],
        wordAllocation: [300, state.contentLength ? state.contentLength - 600 : 1400, 300],
        messaging: 'Comprehensive coverage of ' + state.topic,
        callToAction: 'Expert guidance and actionable insights'
      },
      seoStrategy: {
        primaryKeyword: state.topic,
        secondaryKeywords: [state.topic + ' guide', state.topic + ' tips'],
        titleApproach: 'How-to format with primary keyword',
        metaApproach: 'Benefit-focused with primary keyword'
      },
      humanizationStrategy: {
        personalVoice: 'Expert but approachable',
        authenticityMarkers: ['personal experience', 'honest opinions'],
        conversationalElements: ['rhetorical questions', 'casual transitions'],
        emotionInjection: 'Enthusiasm and helpfulness'
      }
    };
  }

  private log(state: AgentState, message: string): void {
    const logEntry = {
      agent: this.agentId,
      message,
      timestamp: Date.now()
    };
    
    state.logs.push(logEntry);
    console.log(`[${this.agentId}] ${message}`);
  }

  getCapabilities() {
    return {
      name: 'Writing Agent',
      description: 'Superior content generation with AI detection bypass and SEO optimization',
      inputTypes: ['competitorAnalysis', 'contentPlan', 'researchData'],
      outputTypes: ['generatedContent', 'title', 'metaDescription', 'keywords'],
      dependencies: ['gemini-service'],
      parallel: false
    };
  }

  getMetrics(state: AgentState) {
    const writingLogs = state.logs.filter(log => log.agent === this.agentId);
    return {
      executionTime: writingLogs.length > 0 ? Date.now() - writingLogs[0].timestamp : 0,
      successRate: state.errors.filter(e => e.phase === 'content_generation').length === 0 ? 100 : 0,
      qualityScore: state.generatedContent ? 90 : 0,
      retryCount: state.retryCount,
      errorCount: state.errors.filter(e => e.phase === 'content_generation').length,
      lastExecution: Date.now()
    };
  }
} 