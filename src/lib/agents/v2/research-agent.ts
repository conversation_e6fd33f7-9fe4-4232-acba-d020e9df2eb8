/**
 * Research Agent for Invincible v.2 Multi-Agent System
 * Specialized agent responsible for comprehensive research and data gathering
 */

import { TavilySearchService } from '../../search';
import { NodeWebScraperService } from '../../web-scraper';
import { GeminiService } from '../../gemini';
import { KnowledgeBase } from '../../knowledge-base';
import { AgentState, AgentPhase, ResearchAgentConfig } from './types';

export class ResearchAgent {
  private searchService: TavilySearchService;
  private webScraperService: NodeWebScraperService;
  private geminiService: GeminiService;
  private config: ResearchAgentConfig;
  private agentId: string;

  constructor(config: Partial<ResearchAgentConfig> = {}) {
    this.config = {
      searchDepth: config.searchDepth ?? 10,
      maxUrls: config.maxUrls ?? 15,
      parallelSearches: config.parallelSearches ?? 3,
      researchQueries: config.researchQueries ?? 12
    };
    
    this.agentId = 'research-agent';
    this.searchService = new TavilySearchService();
    this.webScraperService = new NodeWebScraperService();
    this.geminiService = new GeminiService();
  }

  async execute(state: AgentState): Promise<AgentState> {
    const startTime = Date.now();
    this.log(state, '🔬 Research Agent: Starting comprehensive research phase');

    try {
      // Update state to research phase
      state.currentPhase = AgentPhase.RESEARCH;
      
      // Step 1: Primary search and URL extraction
      const primaryUrls = await this.performPrimarySearch(state);
      state.primaryUrls = primaryUrls;
      this.log(state, `📊 Primary search complete: ${primaryUrls.length} URLs found`);

      // Step 2: Generate intelligent research queries
      const researchQueries = await this.generateResearchQueries(state, primaryUrls);
      state.researchQueries = researchQueries;
      this.log(state, `🧠 Generated ${researchQueries.length} intelligent research queries`);

      // Step 3: Execute comprehensive research
      const researchData = await this.executeComprehensiveResearch(state, researchQueries);
      state.researchData = researchData;
      this.log(state, `📚 Research complete: ${researchData.length} data sources gathered`);

      // Step 4: Analyze and structure research findings
      await this.analyzeResearchFindings(state);
      this.log(state, '✅ Research analysis and structuring complete');

      // Mark research phase as complete
      state.completedPhases.push(AgentPhase.RESEARCH);
      state.currentPhase = AgentPhase.COMPETITION_ANALYSIS;

      const executionTime = Date.now() - startTime;
      this.log(state, `🎯 Research Agent completed in ${executionTime}ms`);

      return state;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown research error';
      state.errors.push({
        phase: 'research',
        error: errorMessage,
        timestamp: Date.now()
      });
      
      this.log(state, `❌ Research Agent failed: ${errorMessage}`);
      state.currentPhase = AgentPhase.FAILED;
      
      return state;
    }
  }

  private async performPrimarySearch(state: AgentState): Promise<Array<{url: string, content: string, title: string}>> {
    this.log(state, `🔍 Searching for "${state.topic}" - targeting ${this.config.maxUrls} URLs`);
    
    try {
      // Search for the exact topic
      const searchResults = await this.searchService.search(state.topic, this.config.maxUrls);

      // Extract top URLs
      const topUrls = searchResults.items.slice(0, this.config.maxUrls).map((result: any) => result.link);
      this.log(state, `📋 Selected ${topUrls.length} URLs for scraping`);

      // Scrape URLs in parallel batches
      const batchSize = Math.min(this.config.parallelSearches, topUrls.length);
      const scrapedData: Array<{url: string, content: string, title: string}> = [];

      for (let i = 0; i < topUrls.length; i += batchSize) {
        const batch = topUrls.slice(i, i + batchSize);
        const batchPromises = batch.map(async (url) => {
          try {
            const scraped = await this.webScraperService.scrapeUrl(url);
            return {
              url,
              content: scraped.content || '',
              title: scraped.title || `Content from ${url}`
            };
          } catch (error) {
            this.log(state, `⚠️ Failed to scrape ${url}: ${error}`);
            return null;
          }
        });

        const batchResults = await Promise.all(batchPromises);
        scrapedData.push(...batchResults.filter(result => result !== null) as Array<{url: string, content: string, title: string}>);
        
        this.log(state, `📄 Scraped batch ${Math.floor(i/batchSize) + 1}: ${batchResults.filter(r => r).length}/${batch.length} successful`);
      }

      return scrapedData.filter(data => data.content.length > 500); // Filter out thin content
      
    } catch (error) {
      this.log(state, `❌ Primary search failed: ${error}`);
      throw error;
    }
  }

  private async generateResearchQueries(state: AgentState, primaryUrls: Array<{url: string, content: string, title: string}>): Promise<string[]> {
    this.log(state, '🧠 Analyzing content to generate intelligent research queries');

    const contentSample = primaryUrls.slice(0, 5).map(url => ({
      title: url.title,
      content: url.content.substring(0, 2000) // First 2000 chars
    }));

    const analysisPrompt = `You are an expert research strategist analyzing content about "${state.topic}". 

**Primary Content Analysis:**
${JSON.stringify(contentSample, null, 2)}

**Custom Instructions:**
${state.customInstructions || 'None'}

**Target Audience:** ${state.targetAudience || 'General audience'}
**Content Length:** ${state.contentLength || 2000} words
**Tone:** ${state.tone || 'Professional'}

Generate ${this.config.researchQueries} specific, targeted research queries that will help create the most comprehensive article on this topic. Focus on:

1. **Content Gaps**: What information is missing from existing content?
2. **Deep Dive Topics**: What aspects need more detailed exploration?
3. **Current Trends**: What recent developments should be covered?
4. **Practical Applications**: What real-world examples and use cases exist?
5. **Expert Insights**: What professional perspectives are needed?
6. **Statistical Data**: What metrics and studies support the topic?
7. **Related Concepts**: What connected topics enhance understanding?

Return ONLY a JSON array of research queries, no additional text:
["query1", "query2", "query3", ...]`;

    try {
      const response = await this.geminiService.generateContent(
        analysisPrompt,
        { temperature: 0.7, maxOutputTokens: 2048 },
        'Research Query Generation'
      );

      const queries = this.parseJsonResponse(response.response);
      return Array.isArray(queries) ? queries.slice(0, this.config.researchQueries) : [];
      
    } catch (error) {
      this.log(state, `⚠️ Query generation failed, using fallback queries: ${error}`);
      
      // Fallback queries based on topic
      return [
        `${state.topic} complete guide`,
        `${state.topic} best practices`,
        `${state.topic} examples`,
        `${state.topic} tutorial`,
        `${state.topic} tips`
      ].slice(0, this.config.researchQueries);
    }
  }

  private parseJsonResponse(response: string): any {
    try {
      // Remove markdown code blocks if present
      let cleanResponse = response.trim();
      
      // Remove ```json and ``` markers
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // Try to parse the cleaned response
      return JSON.parse(cleanResponse.trim());
    } catch (error) {
      // If JSON parsing fails, return a fallback structure
      console.warn('Failed to parse JSON response, using fallback:', error);
      return [];
    }
  }

  private async executeComprehensiveResearch(state: AgentState, queries: string[]): Promise<Array<{query: string, results: any[], source: string}>> {
    this.log(state, `🔎 Executing ${queries.length} research queries with parallel processing`);
    
    const researchData: Array<{query: string, results: any[], source: string}> = [];
    const batchSize = this.config.parallelSearches;

    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (query) => {
        try {
          const searchResults = await this.searchService.search(query, 7);

          return {
            query,
            results: searchResults.items,
            source: 'tavily_search'
          };
        } catch (error) {
          this.log(state, `⚠️ Research query failed: ${query} - ${error}`);
          return {
            query,
            results: [],
            source: 'failed'
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      researchData.push(...batchResults);
      
      this.log(state, `📊 Research batch ${Math.floor(i/batchSize) + 1} complete: ${batchResults.length} queries processed`);
    }

    return researchData.filter(data => data.results.length > 0);
  }

  private async analyzeResearchFindings(state: AgentState): Promise<void> {
    this.log(state, '📈 Analyzing research findings and extracting insights');

    if (!state.researchData || !state.primaryUrls) {
      throw new Error('Research data or primary URLs missing');
    }

    // Calculate research statistics
    const totalDataPoints = state.researchData.reduce((sum, data) => sum + data.results.length, 0);
    const uniqueDomains = new Set();
    
    state.researchData.forEach(data => {
      data.results.forEach(result => {
        try {
          const domain = new URL(result.url).hostname;
          uniqueDomains.add(domain);
        } catch (e) {
          // Invalid URL, skip
        }
      });
    });

    this.log(state, `📊 Research Summary: ${totalDataPoints} data points from ${uniqueDomains.size} unique domains`);

    // Store research metadata in state for other agents
    state.logs.push({
      agent: this.agentId,
      message: `Research phase complete: ${state.primaryUrls.length} primary sources, ${state.researchData.length} research queries, ${totalDataPoints} data points`,
      timestamp: Date.now()
    });
  }

  private log(state: AgentState, message: string): void {
    const logEntry = {
      agent: this.agentId,
      message,
      timestamp: Date.now()
    };
    
    state.logs.push(logEntry);
    console.log(`[${this.agentId}] ${message}`);
  }

  getCapabilities() {
    return {
      name: 'Research Agent',
      description: 'Comprehensive research and data gathering specialist',
      inputTypes: ['topic', 'customInstructions', 'targetAudience'],
      outputTypes: ['primaryUrls', 'researchQueries', 'researchData'],
      dependencies: ['search-service', 'web-scraper', 'gemini-service'],
      parallel: true
    };
  }

  getMetrics(state: AgentState) {
    const researchLogs = state.logs.filter(log => log.agent === this.agentId);
    return {
      executionTime: researchLogs.length > 0 ? Date.now() - researchLogs[0].timestamp : 0,
      successRate: state.errors.filter(e => e.phase === 'research').length === 0 ? 100 : 0,
      qualityScore: state.researchData ? Math.min(100, state.researchData.length * 8.33) : 0,
      retryCount: state.retryCount,
      errorCount: state.errors.filter(e => e.phase === 'research').length,
      lastExecution: Date.now()
    };
  }
} 