import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { KaibanJSAgent, type KaibanJSConfig } from '@/lib/agents/kaibanjs/KaibanJSAgent';
import { createProgressManager } from '@/lib/progress-manager';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { topic, customInstructions, targetAudience, contentLength, tone, keywords } = body;
    
    if (!topic) {
      return NextResponse.json({ error: 'Topic is required' }, { status: 400 });
    }

    console.log('🤖 Starting KaibanJS v1 execution for goal:', topic);
    
    // Create task context
    const config: KaibanJSConfig = {
      topic,
      customInstructions,
      targetAudience,
      contentLength: contentLength || 2000,
      tone: tone || 'professional',
      keywords: keywords || [],
      searchDepth: 5,
      competitorCount: 5,
      maxIterations: 3,
      qualityThreshold: 80,
      enableAutonomousMode: true
    };

    // Create progress manager
    const progressManager = createProgressManager(`kaibanjs_${Date.now()}`);
    
    // Initialize progress
    progressManager.updateProgress(5, 'Initializing KaibanJS v1 Multi-Agent System...');
    console.log('📈 Progress tracking initialized');

    // Create and configure agent
    const agent = new KaibanJSAgent(config);
    
    // Set up progress tracking
    let currentProgress = 10;
    const progressInterval = setInterval(() => {
      const status = agent.getWorkflowStatus();
      const completionRate = (status.completedTasks / status.totalTasks) * 100;
      
      if (completionRate > currentProgress) {
        currentProgress = Math.min(completionRate, 90);
        progressManager.updateProgress(
          currentProgress, 
          `Phase: ${status.currentPhase} (${status.completedTasks}/${status.totalTasks} tasks completed)`
        );
      }
    }, 2000);

    // Execute the agent
    const result = await agent.execute();
    
    // Clear progress tracking
    clearInterval(progressInterval);
    
    // Final progress update
    progressManager.updateProgress(100, 'KaibanJS v1 execution completed successfully!');
    
    // Get basic insights
    const insights = {
      capabilities: agent.getCapabilities(),
      qualityScore: result.insights?.qualityScore || 85,
      totalTasks: result.insights?.totalTasks || 5,
      executionTime: result.insights?.executionTime || 0,
      agentCollaboration: result.insights?.agentCollaboration || {},
      workflowStatus: agent.getWorkflowStatus()
    };
    
    return NextResponse.json({
      success: result.success,
      content: result.content,
      insights,
      error: result.error,
      message: result.success ? 'KaibanJS v1 execution completed successfully' : 'KaibanJS v1 execution failed'
    });

  } catch (error) {
    console.error('❌ KaibanJS v1 execution error:', error);
    return NextResponse.json(
      { error: 'KaibanJS v1 execution failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Return KaibanJS agent capabilities and status
    return NextResponse.json({
      available: true,
      agent: 'KaibanJS v1 - Multi-Agent Content System',
      version: '1.0.0',
      description: 'Autonomous multi-agent system with supervisor following invincible v1 workflow',
      capabilities: [
        'Autonomous multi-agent workflow',
        'Primary research with exact keyword targeting',
        'Comprehensive competitive analysis',
        'Superior content generation',
        'Quality assurance and optimization',
        'Workflow supervision and coordination',
        'Real-time collaboration monitoring',
        'Human writing pattern implementation',
        'SEO/GEO/AEO optimization',
        'AI detection bypass techniques'
      ],
      agents: [
        {
          name: 'Research Specialist',
          role: 'Primary Research & Data Collection',
          capabilities: ['Web scraping', 'Competitor analysis', 'Content analysis', 'Research query generation']
        },
        {
          name: 'Competition Analyst',
          role: 'Competitive Analysis & Gap Identification',
          capabilities: ['Gap analysis', 'SEO insights', 'Positioning strategies', 'Market intelligence']
        },
        {
          name: 'Content Writer',
          role: 'Superior Content Generation',
          capabilities: ['Human-like writing', 'SEO optimization', 'AI detection bypass', 'Content structuring']
        },
        {
          name: 'Quality Assurance',
          role: 'Content Quality & Optimization',
          capabilities: ['Quality assessment', 'Fact-checking', 'Optimization recommendations', 'Performance metrics']
        },
        {
          name: 'Workflow Supervisor',
          role: 'Autonomous Workflow Management',
          capabilities: ['Workflow orchestration', 'Quality control', 'Autonomous decisions', 'Final assessment']
        }
      ],
      supportedGoals: [
        'Create comprehensive articles',
        'Research and analysis tasks',
        'Competitive intelligence',
        'Content optimization',
        'Quality improvement',
        'SEO-optimized content',
        'Multi-agent collaboration'
      ],
      workflow: {
        steps: 5,
        description: 'Multi-agent system with supervisor for autonomous content creation',
        phases: [
          'Primary Research & Data Collection',
          'Competitive Analysis & Gap Identification',
          'Superior Content Generation',
          'Quality Assurance & Optimization',
          'Workflow Supervision & Final Assessment'
        ]
      },
      features: [
        'Autonomous operation',
        'Real-time monitoring',
        'Quality assurance',
        'Competitive analysis',
        'Human writing patterns',
        'SEO optimization',
        'Multi-agent coordination',
        'Workflow supervision'
      ]
    });

  } catch (error) {
    console.error('❌ Error getting KaibanJS status:', error);
    return NextResponse.json(
      { error: 'Failed to get KaibanJS status' },
      { status: 500 }
    );
  }
} 