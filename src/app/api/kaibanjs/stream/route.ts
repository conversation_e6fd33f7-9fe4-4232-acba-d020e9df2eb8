import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { KaibanJSAgent, type KaibanJSConfig } from '@/lib/agents/kaibanjs/KaibanJSAgent';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { topic, customInstructions, targetAudience, contentLength, tone, keywords } = body;
    
    if (!topic) {
      return NextResponse.json({ error: 'Topic is required' }, { status: 400 });
    }

    console.log('🚀 Starting KaibanJS v1 streaming execution for:', topic);
    
    // Create task context
    const config: KaibanJSConfig = {
      topic,
      customInstructions,
      targetAudience,
      contentLength: contentLength || 2000,
      tone: tone || 'professional',
      keywords: keywords || [],
      searchDepth: 5,
      competitorCount: 5,
      maxIterations: 3,
      qualityThreshold: 80,
      enableAutonomousMode: true
    };

    // Create stream response
    const stream = new ReadableStream({
      start(controller) {
        const encoder = new TextEncoder();
        
        // Send initial connection status
        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
          type: 'connection',
          status: 'connected',
          timestamp: Date.now(),
          message: 'KaibanJS v1 Multi-Agent System connected'
        })}\n\n`));

        // Initialize agent
        const agent = new KaibanJSAgent(config);
        
        // Send agent initialization
        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
          type: 'init',
          agents: [
            { name: 'Research Specialist', status: 'ready' },
            { name: 'Competition Analyst', status: 'ready' },
            { name: 'Content Writer', status: 'ready' },
            { name: 'Quality Assurance', status: 'ready' },
            { name: 'Workflow Supervisor', status: 'ready' }
          ],
          timestamp: Date.now(),
          message: 'Multi-agent system initialized'
        })}\n\n`));

        // Subscribe to agent updates
        const unsubscribe = agent.subscribeToUpdates((update) => {
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({
            type: 'update',
            status: update.status,
            logs: update.logs,
            timestamp: Date.now()
          })}\n\n`));
        });

        // Start agent execution
        agent.execute().then((result) => {
          // Send completion status - handle large content safely
          const completionData = {
            type: 'completion',
            success: result.success,
            content: result.content,
            insights: result.insights,
            timestamp: Date.now(),
            message: result.success ? 'KaibanJS v1 execution completed successfully' : 'KaibanJS v1 execution failed'
          };
          
          try {
            const jsonString = JSON.stringify(completionData);
            controller.enqueue(encoder.encode(`data: ${jsonString}\n\n`));
          } catch (error) {
            // If JSON is too large, send without content and send content separately
            console.warn('Large JSON detected, sending content separately');
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({
              type: 'completion',
              success: result.success,
              content: '[LARGE_CONTENT]',
              insights: result.insights,
              timestamp: Date.now(),
              message: result.success ? 'KaibanJS v1 execution completed successfully' : 'KaibanJS v1 execution failed'
            })}\n\n`));
            
            // Send content in chunks
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({
              type: 'content',
              content: result.content,
              timestamp: Date.now()
            })}\n\n`));
          }

          // Clean up
          unsubscribe();
          controller.close();
        }).catch((error) => {
          // Send error status
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({
            type: 'error',
            error: error.message,
            timestamp: Date.now(),
            message: 'KaibanJS v1 execution failed'
          })}\n\n`));

          // Clean up
          unsubscribe();
          controller.close();
        });
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('❌ KaibanJS v1 streaming error:', error);
    return NextResponse.json(
      { error: 'KaibanJS v1 streaming failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return NextResponse.json({
      available: true,
      streaming: true,
      agent: 'KaibanJS v1 - Multi-Agent Streaming System',
      version: '1.0.0',
      description: 'Real-time streaming monitoring of KaibanJS v1 multi-agent execution',
      features: [
        'Real-time agent monitoring',
        'Live workflow updates',
        'Task progress tracking',
        'Agent collaboration insights',
        'Error handling and reporting',
        'Completion notifications'
      ],
      events: [
        'connection - Initial connection status',
        'init - Agent initialization',
        'update - Real-time workflow updates',
        'completion - Execution completion',
        'error - Error notifications'
      ]
    });

  } catch (error) {
    console.error('❌ Error getting KaibanJS streaming status:', error);
    return NextResponse.json(
      { error: 'Failed to get KaibanJS streaming status' },
      { status: 500 }
    );
  }
} 