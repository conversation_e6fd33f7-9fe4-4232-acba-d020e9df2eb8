import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { LangGraphAutonomousSupervisor } from '@/lib/agents/autonomous/LangGraphAutonomousSupervisor';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { goal, config } = await req.json();
    
    if (!goal) {
      return NextResponse.json({ error: 'Goal is required' }, { status: 400 });
    }

    console.log('🤖 Starting autonomous execution for goal:', goal);
    
    // Initialize LangGraph-inspired autonomous supervisor
    const supervisor = new LangGraphAutonomousSupervisor(config);
    
    // Execute autonomously
    const result = await supervisor.executeAutonomous(goal);
    
    // Get enhanced insights
    const insights = {
      capabilities: supervisor.getCapabilities(),
      qualityScore: result.insights?.finalQuality || result.qualityScore || 85,
      totalTasks: result.insights?.totalDecisions || 1,
      totalSources: result.insights?.totalSources || 0,
      competitorsAnalyzed: result.insights?.competitorsAnalyzed || 0,
      iterationsCompleted: result.insights?.iterationsCompleted || 1
    };
    
    return NextResponse.json({
      success: true,
      result,
      insights,
      message: 'Autonomous execution completed successfully'
    });

  } catch (error) {
    console.error('❌ Autonomous execution error:', error);
    return NextResponse.json(
      { error: 'Autonomous execution failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Return autonomous supervisor capabilities and status
    const supervisor = new LangGraphAutonomousSupervisor();
    
    return NextResponse.json({
      available: true,
      capabilities: supervisor.getCapabilities(),
      supportedGoals: [
        'Create comprehensive articles with web research',
        'Research and competitive analysis',
        'Content optimization and quality enhancement',
        'Self-improving content generation',
        'Multi-agent orchestrated workflows',
        'Intelligent planning with web access'
      ],
      features: [
        'Web-enabled intelligent planning (eliminates generic fallbacks)',
        'True autonomous supervisor orchestration',
        'Quality-based iterative enhancement',
        'Dynamic agent selection and routing',
        'Real-time quality assessment',
        'Context-aware content generation'
      ]
    });

  } catch (error) {
    console.error('❌ Error getting autonomous status:', error);
    return NextResponse.json(
      { error: 'Failed to get autonomous status' },
      { status: 500 }
    );
  }
} 