'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { 
  ArrowLeft, 
  Lightbulb, 
  Network, 
  Zap, 
  Target, 
  Search, 
  TrendingUp, 
  Eye, 
  Rocket,
  Plus,
  X,
  Settings,
  Play,
  ChevronRight,
  Shield,
  Crown,
  FileText,
  Users,
  Globe,
  Clock,
  BarChart,
  Monitor,
  Activity,
  Bot,
  Layers,
  Sparkles,
  Brain,
  Workflow,
  CheckCircle2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import KaibanJSStreamingUI from '@/components/KaibanJSStreamingUI';

interface KaibanJSConfig {
  topic: string;
  customInstructions?: string;
  targetAudience?: string;
  contentLength?: number;
  tone?: string;
  keywords?: string[];
  searchDepth?: number;
  competitorCount?: number;
  maxIterations?: number;
  qualityThreshold?: number;
  enableAutonomousMode?: boolean;
}

export default function KaibanJSPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [isExecuting, setIsExecuting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showConfig, setShowConfig] = useState(true);
  const [config, setConfig] = useState<KaibanJSConfig>({
    topic: '',
    customInstructions: '',
    targetAudience: 'general audience',
    contentLength: 2000,
    tone: 'professional',
    keywords: [],
    searchDepth: 5,
    competitorCount: 5,
    maxIterations: 3,
    qualityThreshold: 80,
    enableAutonomousMode: true
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (!session) {
      router.push('/login');
    }
  }, [session, router]);

  if (!session) {
    return null;
  }

  const handleExecute = () => {
    if (!config.topic.trim()) {
      alert('Please enter a topic to generate content for');
      return;
    }
    
    setIsExecuting(true);
    setShowConfig(false);
  };

  const handleComplete = async (result: any) => {
    console.log('🔍 KaibanJS execution completed:', result);
    console.log('🔍 Result structure:', {
      hasContent: !!result?.content,
      contentLength: result?.content?.length || 0,
      hasSuccess: !!result?.success,
      hasInsights: !!result?.insights,
      keys: Object.keys(result || {})
    });
    setIsExecuting(false);
    
    // Save the generated content to the database
    if (result?.content) {
      setIsSaving(true);
      try {
        // Generate a better title from the topic
        const generateTitle = (topic: string) => {
          const words = topic.split(' ');
          const titleWords = words.map(word => 
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          );
          return titleWords.join(' ');
        };

        const savePayload = {
          title: result.title || generateTitle(config.topic),
          content: result.content,
          type: 'kaibanjs',
          metadata: {
            topic: config.topic,
            tone: config.tone,
            targetAudience: config.targetAudience,
            contentLength: config.contentLength,
            customInstructions: config.customInstructions,
            keywords: config.keywords,
            executionTime: result.insights?.executionTime,
            qualityScore: result.insights?.qualityScore,
            totalTasks: result.insights?.totalTasks,
            agentCollaboration: result.insights?.agentCollaboration,
            generatedAt: new Date().toISOString(),
            system: 'KaibanJS v1 Multi-Agent System'
          },
          tone: config.tone,
          language: 'en'
        };

        console.log('💾 Attempting to save article with payload:', {
          title: savePayload.title,
          contentLength: savePayload.content.length,
          type: savePayload.type,
          metadataKeys: Object.keys(savePayload.metadata)
        });

        const response = await fetch('/api/articles/store', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(savePayload)
        });

        console.log('📡 API Response Status:', response.status, response.statusText);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ API Error Response:', errorText);
          throw new Error(`Failed to save article: ${response.status} ${errorText}`);
        }

        const savedArticle = await response.json();
        console.log('✅ Article saved successfully:', savedArticle);
        
        // Show success message or navigate to content library
        alert('Article saved successfully! You can find it in your content library.');
        
      } catch (error) {
        console.error('❌ Error saving article:', error);
        console.error('❌ Error details:', {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined
        });
        alert(`Error saving article: ${error instanceof Error ? error.message : String(error)}`);
      } finally {
        setIsSaving(false);
      }
    } else {
      console.log('⚠️ No content to save - result does not have content property');
    }
  };

  const handleError = (error: string) => {
    console.error('KaibanJS execution error:', error);
    setIsExecuting(false);
  };

  const handleReset = () => {
    setIsExecuting(false);
    setShowConfig(true);
    setConfig(prev => ({ ...prev, topic: '' }));
  };

  const agents = [
    {
      name: 'Research Specialist',
      role: 'Primary Research & Data Collection',
      icon: Search,
      color: 'from-blue-500 to-cyan-500',
      description: 'Conducts comprehensive research using exact keyword searches and competitor analysis'
    },
    {
      name: 'Competition Analyst',
      role: 'Competitive Analysis & Gap Identification',
      icon: TrendingUp,
      color: 'from-purple-500 to-pink-500',
      description: 'Analyzes competitor content to identify gaps and superior positioning strategies'
    },
    {
      name: 'Content Writer',
      role: 'Superior Content Generation',
      icon: FileText,
      color: 'from-green-500 to-emerald-500',
      description: 'Creates exceptional, human-like content that surpasses all competitors'
    },
    {
      name: 'Quality Assurance',
      role: 'Content Quality & Optimization',
      icon: Shield,
      color: 'from-orange-500 to-red-500',
      description: 'Ensures content meets highest quality standards and optimization requirements'
    },
    {
      name: 'Workflow Supervisor',
      role: 'Autonomous Workflow Management',
      icon: Crown,
      color: 'from-yellow-500 to-orange-500',
      description: 'Orchestrates the entire workflow and makes autonomous decisions'
    }
  ];

  if (isExecuting) {
    return (
      <KaibanJSStreamingUI
        topic={config.topic}
        contentLength={config.contentLength}
        tone={config.tone}
        targetAudience={config.targetAudience}
        customInstructions={config.customInstructions}
        onComplete={handleComplete}
        onError={handleError}
        isSaving={isSaving}
      />
    );
  }

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
        
        {/* KaibanJS-style animated background */}
        <div className="absolute inset-0 opacity-10">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-px bg-gradient-to-b from-transparent via-emerald-400 to-transparent"
              style={{
                left: `${(i * 5)}%`,
                height: '100%'
              }}
              animate={{
                opacity: [0.1, 0.3, 0.1],
                scaleY: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 2 + Math.random() * 3,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>

        {/* Floating particles */}
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-emerald-400 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.8, 0.2],
              scale: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 3 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 3
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 p-6 max-w-7xl mx-auto">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <Link 
            href="/dashboard" 
            className="flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group"
          >
            <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
            <span className="font-medium">Back to Dashboard</span>
          </Link>
          
          <div className="h-6 w-px bg-white/20" />
          
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-600 to-cyan-600 rounded-xl blur-lg opacity-70" />
              <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                <Lightbulb className="w-6 h-6 text-white" />
              </div>
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">
                KaibanJS v1 Multi-Agent System
              </h1>
              <p className="text-sm text-gray-400">
                Autonomous Content Generation with Supervisor
              </p>
            </div>
          </div>
        </motion.div>

        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center space-x-3 mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-600 to-cyan-600 rounded-2xl blur-xl opacity-60" />
              <div className="relative bg-black rounded-2xl p-4 border border-emerald-500/50">
                <Network className="w-12 h-12 text-emerald-400" />
              </div>
            </div>
            <div className="text-left">
              <h2 className="text-5xl font-bold bg-gradient-to-r from-white via-emerald-200 to-cyan-200 bg-clip-text text-transparent">
                KaibanJS v1
              </h2>
              <p className="text-xl text-gray-400 mt-2">
                Multi-Agent Content Generation System
              </p>
            </div>
          </div>
          
          <p className="text-gray-400 text-lg max-w-3xl mx-auto mb-8">
            Experience the future of autonomous AI content creation with KaibanJS. Our multi-agent system follows the 
            proven invincible v1 workflow but with specialized agents working in perfect harmony under supervisor guidance.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Network className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-white mb-2">Multi-Agent System</h3>
              <p className="text-sm text-gray-400">
                5 specialized agents working together under supervisor guidance
              </p>
            </div>
            
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Crown className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-white mb-2">Autonomous Supervision</h3>
              <p className="text-sm text-gray-400">
                Intelligent supervisor makes autonomous decisions and ensures quality
              </p>
            </div>
            
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Workflow className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-white mb-2">Invincible v1 Workflow</h3>
              <p className="text-sm text-gray-400">
                Proven workflow enhanced with multi-agent collaboration
              </p>
            </div>
          </div>
        </motion.div>

        {/* Agent Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-12"
        >
          <h3 className="text-2xl font-bold text-white mb-6 text-center">
            Meet Your AI Agent Team
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {agents.map((agent, index) => {
              const Icon = agent.icon;
              return (
                <motion.div
                  key={agent.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-black/60 backdrop-blur-xl border border-white/10 rounded-xl p-6 hover:border-emerald-500/50 transition-all duration-300"
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="relative">
                      <div className={`absolute inset-0 bg-gradient-to-r ${agent.color} rounded-lg blur-sm opacity-50`} />
                      <div className="relative bg-black rounded-lg p-3 border border-white/20">
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white">{agent.name}</h4>
                      <p className="text-sm text-gray-400">{agent.role}</p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-300">{agent.description}</p>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Configuration Panel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/60 backdrop-blur-xl border border-white/10 rounded-2xl p-8"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-white">Configuration</h3>
            <div className="flex items-center space-x-2">
              <Settings className="w-5 h-5 text-gray-400" />
              <span className="text-sm text-gray-400">Multi-Agent Settings</span>
            </div>
          </div>

          <div className="space-y-6">
            {/* Topic Input */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-white/90">
                Target Topic <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                value={config.topic}
                onChange={(e) => setConfig({ ...config, topic: e.target.value })}
                className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-emerald-500/50 transition-all"
                placeholder="e.g., The 5 Best AI Agents of 2025"
              />
            </div>

            {/* Configuration Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Content Length */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-white/90">
                  Content Length (words)
                </label>
                <input
                  type="number"
                  value={config.contentLength}
                  onChange={(e) => setConfig({ ...config, contentLength: parseInt(e.target.value) })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:bg-white/10 focus:border-emerald-500/50 transition-all"
                  min="500"
                  max="10000"
                />
              </div>

              {/* Tone */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-white/90">
                  Tone
                </label>
                <select
                  value={config.tone}
                  onChange={(e) => setConfig({ ...config, tone: e.target.value })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:bg-white/10 focus:border-emerald-500/50 transition-all"
                >
                  <option value="professional">Professional</option>
                  <option value="casual">Casual</option>
                  <option value="academic">Academic</option>
                  <option value="conversational">Conversational</option>
                  <option value="technical">Technical</option>
                </select>
              </div>

              {/* Target Audience */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-white/90">
                  Target Audience
                </label>
                <input
                  type="text"
                  value={config.targetAudience}
                  onChange={(e) => setConfig({ ...config, targetAudience: e.target.value })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-emerald-500/50 transition-all"
                  placeholder="e.g., AI enthusiasts, developers, business owners"
                />
              </div>

              {/* Quality Threshold */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-white/90">
                  Quality Threshold (%)
                </label>
                <input
                  type="number"
                  value={config.qualityThreshold}
                  onChange={(e) => setConfig({ ...config, qualityThreshold: parseInt(e.target.value) })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:bg-white/10 focus:border-emerald-500/50 transition-all"
                  min="70"
                  max="100"
                />
              </div>
            </div>

            {/* Custom Instructions */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-white/90">
                Custom Instructions (Optional)
              </label>
              <textarea
                value={config.customInstructions}
                onChange={(e) => setConfig({ ...config, customInstructions: e.target.value })}
                rows={4}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-emerald-500/50 transition-all resize-none"
                placeholder="Any specific instructions for the agents..."
              />
            </div>

            {/* Execute Button */}
            <div className="flex justify-center pt-6">
              <button
                onClick={handleExecute}
                disabled={!config.topic.trim()}
                className="group relative px-8 py-4 bg-gradient-to-r from-emerald-600 to-cyan-600 rounded-xl font-medium text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:from-emerald-500 hover:to-cyan-500 hover:scale-105"
              >
                <div className="flex items-center space-x-3">
                  <Play className="w-5 h-5" />
                  <span>Start Multi-Agent Execution</span>
                  <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </div>
              </button>
            </div>
          </div>
        </motion.div>

        {/* Features */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-12 text-center"
        >
          <h3 className="text-2xl font-bold text-white mb-6">
            Why Choose KaibanJS v1?
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                icon: Network,
                title: 'Multi-Agent Collaboration',
                description: 'Specialized agents working together for superior results'
              },
              {
                icon: Crown,
                title: 'Autonomous Supervision',
                description: 'Intelligent supervisor ensures quality and coordination'
              },
              {
                icon: Workflow,
                title: 'Proven Workflow',
                description: 'Based on successful invincible v1 methodology'
              },
              {
                icon: CheckCircle2,
                title: 'Quality Assurance',
                description: 'Built-in quality checks and optimization'
              }
            ].map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4 mx-auto">
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-white mb-2">{feature.title}</h4>
                  <p className="text-sm text-gray-400">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </motion.div>
      </div>
    </div>
  );
} 