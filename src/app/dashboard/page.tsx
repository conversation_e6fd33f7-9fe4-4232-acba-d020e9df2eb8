'use client'

import { useState, useEffect, Suspense } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import dynamic from 'next/dynamic'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { 
  Sparkles,
  Zap,
  Globe,
  Shield,
  Rocket,
  ArrowRight,
  Plus,
  Filter,
  Search,
  Bell,
  Grid,
  List,
  ChevronRight,
  TrendingUp,
  Clock,
  FileText,
  Award,
  Layers,
  Activity,
  Command,
  Settings,
  LogOut,
  User,
  HelpCircle,
  Home,
  PenTool,
  Mail,
  Share2,
  Youtube,
  Menu,
  X,
  ChevronLeft,
  Palette,
  Wand2,
  Lightbulb,
  Target,
  Compass,
  Cpu,
  BarChart,
  MessageSquare,
  Crown,
  Edit3,
  Video,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'
import ProfileButton from '@/components/ProfileButton'
import RecentContent from '@/components/dashboard/RecentContent'

// Dynamically import the 3D components to avoid SSR issues
const InvincibleOrb = dynamic(() => import('@/components/InvincibleOrb'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="w-16 h-16 border-4 border-violet-500/30 border-t-violet-500 rounded-full animate-spin"></div>
    </div>
  )
})

const BlogPreview = dynamic(() => import('@/components/BlogPreview'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="w-16 h-16 border-4 border-pink-500/30 border-t-pink-500 rounded-full animate-spin"></div>
    </div>
  )
})

const EmailPreview = dynamic(() => import('@/components/EmailPreview'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="w-16 h-16 border-4 border-emerald-500/30 border-t-emerald-500 rounded-full animate-spin"></div>
    </div>
  )
})

const SocialMediaPreview = dynamic(() => import('@/components/SocialMediaPreview'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="w-16 h-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
    </div>
  )
})

const VideoScriptPreview = dynamic(() => import('@/components/VideoScriptPreview'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="w-16 h-16 border-4 border-red-500/30 border-t-red-500 rounded-full animate-spin"></div>
    </div>
  )
})

const VideoAlchemyPreview = dynamic(() => import('@/components/VideoAlchemyPreview'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="w-16 h-16 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin"></div>
    </div>
  )
})

const KaibanJSPreview = dynamic(() => import('@/components/KaibanJSPreview'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="w-16 h-16 border-4 border-emerald-500/30 border-t-emerald-500 rounded-full animate-spin"></div>
    </div>
  )
})

// User profile interface
interface UserProfile {
  id: string
  name: string | null
  email: string | null
  image: string | null
  firstName: string | null
  lastName: string | null
  bio: string | null
  subscription?: {
    plan: string
    status: string
  }
  stats?: {
    totalContent: number
    totalUsage: number
  }
}

// Tool interface
interface Tool {
  id: string
  title: string
  subtitle: string
  description: string
  icon: any
  color: string
  bgGradient: string
  accentColor: string
  stats: {
    generated: number
    quality: number
    avgTime: string
  }
  features: string[]
  href: string
  preview?: React.ReactNode
}

// Modern Dashboard with Sidebar
export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [selectedTool, setSelectedTool] = useState<string>('invincible-agent')
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [showNotifications, setShowNotifications] = useState(false)
  const [hoveredTool, setHoveredTool] = useState<string | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isLoadingProfile, setIsLoadingProfile] = useState(true)
  const [userStats, setUserStats] = useState<any>(null)
  const [isLoadingStats, setIsLoadingStats] = useState(true)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Fetch user profile data
  useEffect(() => {
    if (session?.user) {
      fetchUserProfile()
      fetchUserStats()
    }
  }, [session])

  const fetchUserProfile = async () => {
    try {
      const response = await fetch('/api/user/profile')
      if (response.ok) {
        const data = await response.json()
        setUserProfile(data)
      }
    } catch (error) {
      console.error('Error fetching user profile:', error)
    } finally {
      setIsLoadingProfile(false)
    }
  }

  const fetchUserStats = async () => {
    try {
      const response = await fetch('/api/stats')
      if (response.ok) {
        const data = await response.json()
        setUserStats(data.stats)
      }
    } catch (error) {
      console.error('Error fetching user stats:', error)
    } finally {
      setIsLoadingStats(false)
    }
  }

  // Generate user initials for avatar
  const getUserInitials = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName[0]}${userProfile.lastName[0]}`
    } else if (userProfile?.name) {
      const names = userProfile.name.split(' ')
      return names.length > 1 ? `${names[0][0]}${names[names.length - 1][0]}` : names[0][0]
    } else if (userProfile?.email) {
      return userProfile.email[0].toUpperCase()
    }
    return 'U'
  }

  // Get display name
  const getDisplayName = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName} ${userProfile.lastName}`
    } else if (userProfile?.name) {
      return userProfile.name
    } else if (userProfile?.email) {
      return userProfile.email.split('@')[0]
    }
    return 'User'
  }

  // Loading state
  if (status === 'loading' || isLoadingProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  // Dynamic stats calculation for tools
  const getToolStats = (toolType: string) => {
    if (!userStats?.contentBreakdown) {
      return { generated: 0, quality: 9.0, avgTime: '0 min' }
    }

    const generated = userStats.contentBreakdown[toolType] || 0
    const quality = Math.min(9.8, 8.5 + (generated * 0.05))
    
    const avgTimes: Record<string, string> = {
      'invincible_research': '4 min',
      'blog': '3 min',
      'email': '1 min',
      'social_media': '30 sec',
      'youtube_script': '4 min'
    }

    return {
      generated,
      quality: Math.round(quality * 10) / 10,
      avgTime: avgTimes[toolType] || '2 min'
    }
  }

  // Tools configuration with unique designs
  const tools: Tool[] = [

    {
      id: 'invincible-agent',
      title: 'Invincible V.1',
      subtitle: 'Superior Content',
      description: 'RAG-based content generation that analyzes competition, understands human writing patterns, and creates superior articles that dominate search results.',
      icon: Crown,
      color: 'from-violet-800/80 to-indigo-800/80',
      bgGradient: 'from-violet-950/30 to-indigo-950/30',
      accentColor: 'violet',
      stats: getToolStats('invincible_research'),
      features: ['RAG Research', 'Competitive Analysis', 'Human Writing Style', 'SEO Optimization', 'Superior Quality', 'Knowledge Base'],
      href: '/invincible',
      preview: <InvincibleOrb />
    },
    {
      id: 'kaibanjs-agent',
      title: 'KaibanJS v1',
      subtitle: 'Multi-Agent System',
      description: 'Autonomous multi-agent system with supervisor following invincible v1 workflow. Experience the future of AI collaboration with specialized agents working together.',
      icon: Lightbulb,
      color: 'from-emerald-600 to-cyan-600',
      bgGradient: 'from-emerald-900 to-cyan-900',
      accentColor: 'emerald',
      stats: getToolStats('kaibanjs_agent'),
      features: ['Multi-Agent Workflow', 'Autonomous Supervision', 'Real-time Monitoring', 'Agent Collaboration', 'Quality Assurance', 'Invincible v1 Workflow'],
      href: '/kaibanjs',
      preview: <KaibanJSPreview />
    },
    {
      id: 'invincible-v2',
      title: 'Invincible V.2',
      subtitle: 'Multi-Agent AI',
      description: 'Advanced multi-agent architecture with specialized Research, Competition, Writing, and Quality agents for superior autonomous content generation.',
      icon: Cpu,
      color: 'from-indigo-600 to-purple-600',
      bgGradient: 'from-indigo-900 to-purple-900',
      accentColor: 'indigo',
      stats: getToolStats('invincible_v2'),
      features: ['Multi-Agent System', 'Real-time Monitoring', 'Advanced GEO/AEO', 'AI Detection Bypass', 'Quality Assurance', 'Autonomous Workflow'],
      href: '/invincible-v2',
      preview: <InvincibleOrb />
    },
    {
      id: 'email-generator',
      title: 'Email Composer',
      subtitle: 'Professional Emails',
      description: 'Create compelling email campaigns, newsletters, and personal messages with AI-powered suggestions.',
      icon: Mail,
      color: 'from-emerald-600 to-teal-600',
      bgGradient: 'from-emerald-900 to-teal-900',
      accentColor: 'emerald',
      stats: getToolStats('email'),
      features: ['Templates', 'Personalization', 'A/B Testing', 'Analytics'],
      href: '/generate/email',
      preview: <EmailPreview />
    },
    {
      id: 'tweet-generator',
      title: 'Social Media',
      subtitle: 'Viral Content',
      description: 'Generate engaging tweets, threads, and social media content optimized for maximum engagement.',
      icon: MessageSquare,
      color: 'from-blue-600 to-cyan-600',
      bgGradient: 'from-blue-900 to-cyan-900',
      accentColor: 'blue',
      stats: getToolStats('social_media'),
      features: ['Trending Topics', 'Hashtags', 'Thread Builder', 'Scheduling'],
      href: '/generate/tweet',
      preview: <SocialMediaPreview />
    },
    {
      id: 'blog-generator',
      title: 'Blog Writer',
      subtitle: 'SEO-Optimized',
      description: 'Create comprehensive blog posts with built-in SEO optimization and competitive analysis.',
      icon: FileText,
      color: 'from-pink-600 to-rose-600',
      bgGradient: 'from-pink-900 to-rose-900',
      accentColor: 'pink',
      stats: getToolStats('blog'),
      features: ['SEO Analysis', 'Keyword Research', 'Competition Check', 'Readability'],
      href: '/generate/blog',
      preview: <BlogPreview />
    },
    {
      id: 'youtube-script',
      title: 'Video Scripts',
      subtitle: 'YouTube & More',
      description: 'Write engaging scripts for YouTube videos, tutorials, and presentations with timing cues.',
      icon: Video,
      color: 'from-red-600 to-orange-600',
      bgGradient: 'from-red-900 to-orange-900',
      accentColor: 'red',
      stats: getToolStats('youtube_script'),
      features: ['Hook Generator', 'Scene Breakdown', 'CTA Builder', 'Timing'],
      href: '/generate/youtube',
      preview: <VideoScriptPreview />
    },
    {
      id: 'video-alchemy',
      title: 'Video Alchemy',
      subtitle: 'Video to Article',
      description: 'Transform YouTube videos into SEO-optimized articles by extracting captions and using AI to create comprehensive content.',
      icon: Wand2,
      color: 'from-purple-600 to-pink-600',
      bgGradient: 'from-purple-900 to-pink-900',
      accentColor: 'purple',
      stats: getToolStats('video_alchemy'),
      features: ['Caption Extraction', 'Multi-language', 'SEO/AEO/GEO', 'Human-like Writing', 'Custom Tones', 'Up to 5 Videos'],
      href: '/video-alchemy',
      preview: <VideoAlchemyPreview />
    },
  ]

  const activeTool = tools.find(t => t.id === selectedTool) || tools[0]

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-950/10 via-black to-indigo-950/10" />
        
        {/* Dynamic gradient based on selected tool */}
        <motion.div
          key={activeTool.id}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className={cn(
            "absolute inset-0 bg-gradient-to-br opacity-20",
            activeTool.bgGradient
          )}
        />
        
        {/* Animated orbs */}
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-700/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-700/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Main Layout */}
      <div className="relative z-10 flex min-h-screen">
        {/* Mobile Backdrop */}
        <AnimatePresence>
          {sidebarOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setSidebarOpen(false)}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
            />
          )}
        </AnimatePresence>
        {/* Sidebar */}
        <motion.aside
          initial={{ x: 0 }}
          animate={{ x: sidebarOpen ? 0 : -280 }}
          transition={{ type: "spring", damping: 25 }}
          className="fixed left-0 top-0 h-screen w-[280px] bg-black/60 backdrop-blur-2xl border-r border-white/10 flex flex-col z-50"
        >
          {/* Logo */}
          <div className="p-6 border-b border-white/10">
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-violet-800 to-indigo-800 rounded-xl blur-lg opacity-70" />
                <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">Invincible</h1>
                <p className="text-xs text-gray-400">Creative AI Suite</p>
              </div>
            </Link>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {/* Dashboard Home */}
            <button
              onClick={() => setSelectedTool('')}
              className={cn(
                "w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all",
                !selectedTool
                  ? "bg-white/10 text-white"
                  : "text-gray-400 hover:text-white hover:bg-white/5"
              )}
            >
              <Home className="w-5 h-5" />
              <span className="font-medium">Dashboard</span>
            </button>

            <div className="pt-4 pb-2">
              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-4">
                AI Tools
              </p>
            </div>

            {/* Content Library */}
            <Link href="/content">
              <button className="w-full flex items-center space-x-3 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/5 rounded-xl transition-all">
                <Layers className="w-5 h-5" />
                <div className="text-left">
                  <p className="font-medium">Content Library</p>
                  <p className="text-xs opacity-70">View Past Content</p>
                </div>
              </button>
            </Link>

            {/* Tool Navigation */}
            {tools.map((tool) => (
              <motion.button
                key={tool.id}
                onClick={() => setSelectedTool(tool.id)}
                onMouseEnter={() => setHoveredTool(tool.id)}
                onMouseLeave={() => setHoveredTool(null)}
                className={cn(
                  "w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all relative overflow-hidden",
                  selectedTool === tool.id
                    ? tool.id === 'invincible-agent'
                      ? "bg-gradient-to-r text-white shadow-lg backdrop-blur-xl border border-white/20"
                      : "bg-gradient-to-r text-white shadow-lg"
                    : "text-gray-400 hover:text-white hover:bg-white/5",
                  selectedTool === tool.id && tool.color
                )}
              >
                {/* Hover effect */}
                <AnimatePresence>
                  {hoveredTool === tool.id && selectedTool !== tool.id && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 0.1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className={cn(
                        "absolute inset-0 bg-gradient-to-r",
                        tool.color
                      )}
                    />
                  )}
                </AnimatePresence>

                <div className="relative z-10 flex items-center space-x-3">
                  <tool.icon className="w-5 h-5" />
                  <div className="text-left">
                    <p className="font-medium">{tool.title}</p>
                    <p className="text-xs opacity-70">{tool.subtitle}</p>
                  </div>
                </div>

                {selectedTool === tool.id && (
                  <motion.div
                    layoutId="activeToolIndicator"
                    className="absolute right-2 w-1 h-6 bg-white rounded-full"
                    initial={false}
                    transition={{ type: "spring", damping: 20 }}
                  />
                )}
              </motion.button>
            ))}
          </nav>

          {/* Bottom Actions */}
          <div className="p-4 border-t border-white/10 space-y-2">
            <Link href="/settings">
              <button className="w-full flex items-center space-x-3 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/5 rounded-xl transition-all">
                <Settings className="w-5 h-5" />
                <span>Settings</span>
              </button>
            </Link>
            <button className="w-full flex items-center space-x-3 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/5 rounded-xl transition-all">
              <HelpCircle className="w-5 h-5" />
              <span>Help & Support</span>
            </button>
          </div>
        </motion.aside>

        {/* Sidebar Toggle */}
        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className={cn(
            "fixed top-6 z-50 p-2 bg-black/60 backdrop-blur-xl border border-white/10 rounded-lg transition-all",
            sidebarOpen ? "left-[268px]" : "left-4"
          )}
        >
          {sidebarOpen ? <ChevronLeft className="w-5 h-5 text-white" /> : <Menu className="w-5 h-5 text-white" />}
        </button>

        {/* Main Content Area */}
        <main className={cn(
          "flex-1 min-h-screen transition-all duration-300",
          // No margin on mobile (sidebar is overlay), margin on desktop (sidebar pushes content)
          sidebarOpen ? "lg:ml-[280px]" : "ml-0"
        )}>
          {/* Top Bar */}
          <header className="sticky top-0 z-40 backdrop-blur-xl bg-black/40 border-b border-white/10">
            <div className={cn(
              "px-8 py-4 transition-all duration-300",
              // Normal padding when sidebar open, extra when closed to clear toggle button
              sidebarOpen ? "pl-8" : "pl-16"
            )}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-8">
                  <h2 className="text-2xl font-bold text-white">
                    {selectedTool ? activeTool.title : 'Dashboard Overview'}
                  </h2>
                  
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search tools, features..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-80 pl-10 pr-4 py-2.5 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all"
                    />
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-4">
                  <button
                    className="relative p-2.5 text-gray-400 hover:text-white transition-colors"
                    onClick={() => setShowNotifications(!showNotifications)}
                  >
                    <Bell className="w-5 h-5" />
                    <span className="absolute top-1 right-1 w-2 h-2 bg-violet-500 rounded-full" />
                  </button>

                                      <ProfileButton 
                    userProfile={userProfile} 
                    className="pl-4 border-l border-white/10"
                  />
                </div>
              </div>
            </div>
          </header>

          {/* Content Area */}
          <div className={cn(
            "p-8 pb-16 transition-all duration-300",
            // Normal padding when sidebar open, extra when closed to clear toggle button
            sidebarOpen ? "pl-8" : "pl-16"
          )}>
            <AnimatePresence mode="wait">
              {selectedTool ? (
                <ToolDetails key={selectedTool} tool={activeTool} />
              ) : (
                <DashboardOverview key="overview" tools={tools} userProfile={userProfile} getDisplayName={getDisplayName} userStats={userStats} isLoadingStats={isLoadingStats} />
              )}
            </AnimatePresence>
          </div>
        </main>
      </div>
    </div>
  )
}

// Tool Details Component
function ToolDetails({ tool }: { tool: Tool }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-8"
    >
      {/* Hero Section */}
      <div className={cn(
        "relative overflow-hidden rounded-3xl backdrop-blur-xl border p-8",
        tool.id === 'invincible-agent'
          ? "bg-gradient-to-br from-white/10 to-white/5 border-white/20 shadow-2xl"
          : "bg-gradient-to-br from-white/5 to-white/0 border-white/10"
      )}>
        {/* Glass reflection overlay for Invincible */}
        {tool.id === 'invincible-agent' && (
          <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent rounded-3xl" />
        )}
        
        <div className={cn(
          "absolute inset-0 bg-gradient-to-br opacity-20",
          tool.color
        )} />
        
        <div className="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <div className={cn(
                "p-4 rounded-2xl text-white shadow-2xl",
                tool.id === 'invincible-agent'
                  ? "bg-gradient-to-br from-violet-800/70 to-indigo-800/70 backdrop-blur-sm border border-white/30"
                  : cn("bg-gradient-to-br", tool.color)
              )}>
                <tool.icon className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">{tool.title}</h1>
                <p className="text-lg text-gray-300">{tool.subtitle}</p>
              </div>
            </div>

            <p className="text-gray-300 leading-relaxed">
              {tool.description}
            </p>

            <div className="flex flex-wrap gap-3">
              {tool.features.map((feature, index) => (
                <span
                  key={index}
                  className={cn(
                    "px-4 py-2 rounded-full text-sm text-white border",
                    tool.id === 'invincible-agent'
                      ? "bg-white/15 backdrop-blur-sm border-white/30"
                      : "bg-white/10 backdrop-blur-sm border-white/20"
                  )}
                >
                  {feature}
                </span>
              ))}
            </div>

            <div className="flex items-center space-x-4">
              <Link href={tool.href}>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={cn(
                    "px-12 py-6 rounded-2xl font-bold text-lg text-white shadow-2xl transition-all flex items-center space-x-3",
                    tool.id === 'invincible-agent'
                      ? "bg-gradient-to-r from-violet-800/80 to-indigo-800/80 backdrop-blur-sm border border-white/30 hover:from-violet-700/90 hover:to-indigo-700/90"
                      : cn("bg-gradient-to-r", tool.color)
                  )}
                >
                  <Rocket className="w-6 h-6" />
                  <span>Launch {tool.title}</span>
                </motion.button>
              </Link>
            </div>
          </div>

          {/* Preview Area */}
          <div className={cn(
            "relative h-[400px] rounded-2xl overflow-hidden border",
            tool.id === 'invincible-agent'
              ? "bg-black/30 backdrop-blur-sm border-white/20"
              : "bg-black/40 border-white/10"
          )}>
            {tool.preview || (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center space-y-4">
                  <tool.icon className="w-24 h-24 text-white/20 mx-auto" />
                  <p className="text-gray-400">Interactive preview coming soon</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link href={`/content?type=${tool.id === 'invincible-agent' ? 'invincible_research' : tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id.replace('-', '_')}`}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className={cn(
              "backdrop-blur-xl border rounded-2xl p-6 cursor-pointer hover:bg-white/10 transition-all duration-200 group",
              tool.id === 'invincible-agent'
                ? "bg-white/10 border-white/20 shadow-xl hover:border-white/30"
                : "bg-white/5 border-white/10 hover:border-white/20"
            )}
            title={`View all ${tool.title} content in your library`}
          >
            <div className="flex items-center justify-between mb-4">
              <FileText className={cn("w-8 h-8 group-hover:scale-110 transition-transform duration-200", `text-${tool.accentColor}-400`)} />
              <div className="flex items-center space-x-1">
                <TrendingUp className="w-5 h-5 text-emerald-400" />
                <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" />
              </div>
            </div>
            <p className="text-3xl font-bold text-white mb-1 group-hover:text-violet-200 transition-colors">{tool.stats.generated}</p>
            <p className="text-sm text-gray-400 group-hover:text-gray-300 transition-colors">Content Generated - Click to view</p>
            
            {/* Subtle hover indicator */}
            <div className="mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="flex items-center text-xs text-violet-300">
                <Eye className="w-3 h-3 mr-1" />
                <span>View in Content Library</span>
              </div>
            </div>
          </motion.div>
        </Link>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className={cn(
            "backdrop-blur-xl border rounded-2xl p-6",
            tool.id === 'invincible-agent'
              ? "bg-white/10 border-white/20 shadow-xl"
              : "bg-white/5 border-white/10"
          )}
        >
          <div className="flex items-center justify-between mb-4">
            <Award className={cn("w-8 h-8", `text-${tool.accentColor}-400`)} />
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <div
                  key={star}
                  className={cn(
                    "w-3 h-3 rounded-full",
                    star <= Math.round(tool.stats.quality / 2)
                      ? "bg-yellow-400"
                      : "bg-gray-600"
                  )}
                />
              ))}
            </div>
          </div>
          <p className="text-3xl font-bold text-white mb-1">{tool.stats.quality}/10</p>
          <p className="text-sm text-gray-400">Quality Score</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className={cn(
            "backdrop-blur-xl border rounded-2xl p-6",
            tool.id === 'invincible-agent'
              ? "bg-white/10 border-white/20 shadow-xl"
              : "bg-white/5 border-white/10"
          )}
        >
          <div className="flex items-center justify-between mb-4">
            <Clock className={cn("w-8 h-8", `text-${tool.accentColor}-400`)} />
            <Activity className="w-5 h-5 text-blue-400" />
          </div>
          <p className="text-3xl font-bold text-white mb-1">{tool.stats.avgTime}</p>
          <p className="text-sm text-gray-400">Average Time</p>
        </motion.div>
      </div>
    </motion.div>
  )
}

// Dashboard Overview Component
function DashboardOverview({ tools, userProfile, getDisplayName, userStats, isLoadingStats }: { 
  tools: Tool[]
  userProfile: UserProfile | null
  getDisplayName: () => string
  userStats: any
  isLoadingStats: boolean
}) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="space-y-8"
    >
      {/* Welcome Section */}
              <div className="bg-gradient-to-r from-violet-800/20 to-indigo-800/20 backdrop-blur-xl border border-white/10 rounded-3xl p-8">
        <h1 className="text-4xl font-bold text-white mb-4">Welcome back, {getDisplayName()}! ✨</h1>
        <p className="text-xl text-gray-300 mb-6">Your creative AI toolkit is ready. What will you create today?</p>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {isLoadingStats ? (
            // Loading skeleton
            Array.from({ length: 4 }).map((_, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse"
              >
                <div className="h-5 bg-white/10 rounded mb-2"></div>
                <div className="h-8 bg-white/10 rounded mb-1"></div>
                <div className="h-4 bg-white/10 rounded"></div>
              </motion.div>
            ))
          ) : (
            [
              { 
                label: 'Total Created', 
                value: userStats?.totalContent?.toString() || '0', 
                icon: FileText, 
                change: userStats?.trends?.contentGrowth || '+0%' 
              },
              { 
                label: 'Time Saved', 
                value: `${userStats?.timeSavedHours || 0} hrs`, 
                icon: Clock, 
                change: userStats?.trends?.timeEfficiency || '+0%' 
              },
              { 
                label: 'Quality Score', 
                value: `${userStats?.qualityScore || 9.0}/10`, 
                icon: Award, 
                change: userStats?.trends?.qualityImprovement || '+0.0' 
              },
              { 
                label: 'Active Tools', 
                value: userStats?.trends?.toolsActive?.toString() || '0', 
                icon: Layers, 
                change: '+' + (userStats?.trends?.toolsActive || 0)
              }
            ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <stat.icon className="w-5 h-5 text-violet-400" />
                <span className="text-xs text-emerald-400">{stat.change}</span>
              </div>
              <p className="text-2xl font-bold text-white">{stat.value}</p>
              <p className="text-sm text-gray-400">{stat.label}</p>
            </motion.div>
          ))
          )}
        </div>
      </div>

      {/* Tools Grid */}
      <div>
        <h2 className="text-2xl font-bold text-white mb-6">Your AI Tools</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool, index) => (
            <Link key={tool.id} href={tool.href}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8 }}
                className="group relative cursor-pointer"
              >
                <div className={cn(
                  "absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl",
                  tool.color
                )} />
                
                <div className={cn(
                  "relative backdrop-blur-xl border transition-all",
                  tool.id === 'invincible-agent' 
                    ? "bg-black/40 border-white/20 hover:border-white/30 rounded-2xl shadow-2xl"
                    : "bg-black/60 border-white/10 hover:border-white/20 rounded-2xl"
                )}>
                  {/* Glass reflection effect for Invincible */}
                  {tool.id === 'invincible-agent' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl" />
                  )}
                  
                  <div className="relative p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className={cn(
                        "p-3 rounded-xl text-white",
                        tool.id === 'invincible-agent'
                          ? "bg-gradient-to-br from-violet-800/60 to-indigo-800/60 backdrop-blur-sm border border-white/20"
                          : cn("bg-gradient-to-br", tool.color)
                      )}>
                        <tool.icon className="w-6 h-6" />
                      </div>
                      <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
                    </div>

                    <h3 className="text-lg font-semibold text-white mb-1">{tool.title}</h3>
                    <p className="text-sm text-gray-400 mb-4">{tool.subtitle}</p>

                    <div className="flex items-center justify-between text-xs">
                      <Link 
                        href={`/content?type=${tool.id === 'invincible-agent' ? 'invincible_research' : tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id.replace('-', '_')}`}
                        onClick={(e) => e.stopPropagation()}
                        className="hover:bg-white/10 rounded px-2 py-1 transition-colors group/stat"
                        title={`View all ${tool.title} content`}
                      >
                        <span className="text-gray-500 group-hover/stat:text-gray-400">Generated</span>
                        <div className="flex items-center space-x-1">
                          <p className="text-white font-medium group-hover/stat:text-violet-200">{tool.stats.generated}</p>
                          <ArrowRight className="w-3 h-3 text-gray-400 group-hover/stat:text-white opacity-0 group-hover/stat:opacity-100 transition-all" />
                        </div>
                        {/* Tooltip on hover */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover/stat:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                          Click to view content
                        </div>
                      </Link>
                      <div className="text-right">
                        <span className="text-gray-500">Quality</span>
                        <p className="text-white font-medium">{tool.stats.quality}/10</p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Content */}
      <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
        <RecentContent limit={5} showFilters={true} />
      </div>
    </motion.div>
  )
} 